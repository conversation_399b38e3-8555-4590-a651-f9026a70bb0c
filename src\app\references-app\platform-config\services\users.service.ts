import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { environment as env } from 'src/environments/environment';
import { Pagination } from "../../referential/models/pagination.interface";

@Injectable({
  providedIn: 'root'
})
export class UsersService {

  constructor(
    private http: HttpClient,
  ) { }



  getRoles() {
    return this.http.get<any>(`${env.winclient_base_url}/api/admin/user/roles`, { observe: 'body' });
  }

  getUsersPaginated(pagination:Pagination={}){
    return this.http.get<any>(`${env.winclient_base_url}/api/admin/user/list`, { observe: 'body', params: {
      page: Math.ceil(pagination.skip / pagination.pageSize),
      size: pagination.pageSize,
      sort: pagination?.sortField ? `${pagination.sortField},${pagination.sortMethod || 'asc'}` : ''
    }});

  }


  createOrEditUser(user: any) {
    return this.http.post<any>(`${env.winclient_base_url}/api/admin/user/edit`, user, { observe: 'body' });
  }

  
  deleteUser(idHash: string, reason: string) {
    return this.http.delete<any>(`${env.winclient_base_url}/api/admin/user/${idHash}`, { observe: 'body',
        params: {
          reason
        }
     });
  }
}