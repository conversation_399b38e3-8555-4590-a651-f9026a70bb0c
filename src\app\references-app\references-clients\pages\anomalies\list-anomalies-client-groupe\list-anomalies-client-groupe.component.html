<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">List Anomalies Client Groupe</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
        <div class="btn-group" role="group">
          <button type="button" class="btn btn-dark" title="Filtrer" (click)="toggleFilterDrawer(true)"
          appTooltip="Filtrer les anomalies"
          >
            <i class="mdi mdi-filter"></i> Filtrer
          </button>
          <ng-container *ngIf="hasActiveFilters">
            <button class="btn btn-outline-dark" title="Actualiser le filtre actuel" (click)="filterCommand('refresh')">
              <i class="mdi mdi-refresh"></i>
            </button>
            <button class="btn btn-outline-dark" title="Effacer le filtre actuel" (click)="filterCommand('reset')">
              <i class="mdi mdi-filter-remove"></i>
            </button>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>

<kendo-grid [data]="anomalies"  
style="height: calc(100vh - 130px);border-radius: 10px;" class="winClient-stats-grid clickable-grid ref-grid content-wrap custom-sort-grid"
[pageable]="true"
[pageSize]="navigation.pageSize" [skip]="navigation.skip"
[rowClass]="rowClass"
(cellClick)="onCellClick($event)"
[sortable]="{mode: 'single'}"
(sortChange)="onSortChange($event)"
[sort]="AnomalieSort"
>
<kendo-grid-column field="codeClientGroupe" title="Code Groupe" [width]="100">
  <ng-template kendoGridCellTemplate let-dataItem>
     <span class="text-primary text-decoration-underline k-cursor-pointer" (click)="consultClientGroupe(dataItem)">
      <app-copy-cell [value]="dataItem.codeClientGroupe">
      </app-copy-cell>
      <i class="mdi mdi-18px mdi-checkbox-marked-circle ml-auto d-block"
      *ngIf="[AnomalieString.DOUBLON_EXACT, AnomalieString.DOUBLON_PROBABLE].includes(dataItem.anomalie)"
      [ngClass]="{'text-success': dataItem.indexOfGood === 1}"
      (click)="$event.stopPropagation();declareGoodPrimaire(dataItem)"></i>
     </span>
  </ng-template>
</kendo-grid-column>
<kendo-grid-column field="clientGroupe.raisonSociale" title="Raison Sociale" [width]="200">
    <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
</kendo-grid-column>
<kendo-grid-column field="clientGroupe.nomPharmacien" title="Nom Pharmacien" [width]="200">
    <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
</kendo-grid-column>
<kendo-grid-column field="clientGroupe.ville.libelle" title="Ville" [width]="200">
    <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
</kendo-grid-column>
<kendo-grid-column field="clientGroupe.classification" title="Classification" [width]="120">
    <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
</kendo-grid-column>
<kendo-grid-column field="codeClientGroupeDoublon" title="Code Doublon" [width]="120">
  <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
      <app-grid-sort-header [title]="column.title"  
      [active]="navigation.sortField === column.field"
      [direction]="navigation.sortMethod"></app-grid-sort-header>
</ng-template>
  <ng-template kendoGridCellTemplate let-dataItem>
    <span class="text-primary text-decoration-underline k-cursor-pointer" (click)="consultClientGroupe(dataItem, true)"
    *ngIf="[AnomalieString.DOUBLON_EXACT, AnomalieString.DOUBLON_PROBABLE].includes(dataItem.anomalie)"
    
    >
      <app-copy-cell [value]="dataItem.codeClientGroupeDoublon"></app-copy-cell>
      <i class="mdi mdi-18px mdi-checkbox-marked-circle ml-auto d-block" 
      [ngClass]="{'text-success': dataItem.indexOfGood === 2}"
      (click)="$event.stopPropagation();declareGoodSecondaire(dataItem)"></i>
    </span>
  </ng-template>
</kendo-grid-column>
<kendo-grid-column field="pourcentageMatching" title="Matching %" [width]="100">
  <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
  <ng-template kendoGridCellTemplate let-dataItem>
    <span *ngIf="dataItem.pourcentageMatching" class="badge bg-primary">{{dataItem.pourcentageMatching}}%</span>
  </ng-template>
</kendo-grid-column>

<kendo-grid-column field="anomalie" title="Anomalie" [width]="200"></kendo-grid-column>
  <kendo-grid-column field="dateCreationAnomalie" title="Date Anomalie" [width]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      {{dataItem.dateCreationAnomalie | date:'dd/MM/yyyy'}}
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="statut" title="Statut" [width]="80"></kendo-grid-column>
  <kendo-grid-column title="Actions" [width]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <app-action-icon appTooltip="Filtrer les anomalies" [icon]="dataItem.statut === 'ouverte' ? 'check' : 'close'"
      [backgroundColor]="dataItem.statut === 'ouverte' ? 'success' : 'danger'"
      [extendClass]="'circle-lg'" (click)="changerStatutAnomalie(dataItem)">

      </app-action-icon>
    </ng-template>
  </kendo-grid-column>
  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
    let-total="total">
    <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
      [navigation]="navigation" style="width: 100%;"
      (pageChange)="pageChange($event)"></wph-grid-custom-pager>
  </ng-template>
</kendo-grid>



<app-custom-drawer 
  [isOpen]="isDrawerFilterOpen" 
  [width]="'600px'" 
  [title]="'Filtrer Anomalies'"
  (isOpenChange)="isDrawerFilterOpen = $event">

 <div class="p-2">
   <form  [formGroup]="anomalieFilterForm" id="anomalieFilterForm" (ngSubmit)="onFilterSubmit()" appFocusTrap>
        <div class="flex-grow-1">
          <div class="row">
            <div class="col-12 col-sm-6">
              <label for="codeClientGroupe" class="form-label">Code Client Groupe</label>
              <input id="codeClientGroupe" type="search"  formControlName="codeClientGroupe" class="form-control">
            </div>
            <div class="col-12 col-sm-6">
              <label for="anomalieType" class="form-label">Type d'Anomalie</label>
              <select id="anomalieType" formControlName="anomalieType" class="form-select">
                <option *ngFor="let option of anomalieTypeOptions" [ngValue]="option.value">{{ option.label }}</option>
              </select>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-12 col-sm-6">
              <label for="raisonSociale" class="form-label">Raison Sociale</label>
              <input id="raisonSociale" type="search" formControlName="raisonSociale" class="form-control">
            </div>
            <div class="col-12 col-sm-6">
              <label for="nomPharmacien" class="form-label">Nom Pharmacien</label>
              <input id="nomPharmacien" type="search"  formControlName="nomPharmacien" class="form-control">
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-12 col-sm-6">
              <label for="codeClientGroupeDoublon" class="form-label">Code Client Doublon</label>
              <input id="codeClientGroupeDoublon" type="search"  formControlName="codeClientGroupeDoublon" class="form-control">
            </div>
            <div class="col-12 col-sm-6">
              <label for="classification" class="form-label">Classification</label>
              <input id="classification" type="search"  formControlName="classification" class="form-control">
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-12 col-sm-6">
              <label for="pourcentageMatchingMin" class="form-label">Pourcentage Matching Min</label>
              <input id="pourcentageMatchingMin" type="number"  formControlName="pourcentageMatchingMin" class="form-control">
            </div>
            <div class="col-12 col-sm-6">
              <label for="pourcentageMatchingMax" class="form-label">Pourcentage Matching Max</label>
              <input id="pourcentageMatchingMax" type="number"  formControlName="pourcentageMatchingMax" class="form-control">
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-12 col-sm-6">
              <label for="dateCreationAnomalie" class="form-label">Date Création Anomalie</label>
              <app-date-picker formControlName="dateCreationAnomalie"></app-date-picker>
            </div>
            <div class="col-12 col-sm-6">
              <label for="dateTraitement" class="form-label">Date Traitement</label>
              <app-date-picker formControlName="dateTraitement"></app-date-picker>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center">
              <label for="statut" class="form-label me-2 mb-1 mb-sm-0">Statut</label>
              <div class="w-100">
                <app-switch
                  [elements]="statutOptions"
                  formControlName="statut"
                  name="statut"
                  [disabled]="false"
                  [switchClass]="'info'">
                </app-switch>
              </div>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center">
              <label for="statut" class="form-label me-2 mb-1 mb-sm-0">Utilise dans Les platforms</label>
              <div class="w-100">
                <app-switch
                  [elements]="[{ value: null, label: 'Tous' },{ value: true, label: 'Oui' }, { value: false, label: 'Non' }]"
                  formControlName="estUtilise"
                  name="statut"
                  [disabled]="false"
                  [switchClass]="'info'">
                </app-switch>
              </div>
            </div>
          </div>
        </div>
      </form>
 </div>
          <div class="modal-footer justify-content-start" drawer-footer>
        <div class="d-flex flex-wrap gap-2 justify-content-start">
          <button class="btn btn-primary" tabindex="-1" type="submit" form="anomalieFilterForm">Recherche</button>
          <button class="btn btn-dark" tabindex="-1" type="button" (click)="filterCommand('reset')">Vider</button>
        </div>
      </div>
</app-custom-drawer>

<!-- Consultation Client Groupe Drawer -->

  <app-custom-drawer [width]="'70%'" [isOpen]="isConsultDrawerOpen" (isOpenChange)="toggleConsultDrawer($event)"
  title="Consultation Anomalie Client Groupe">
  <div class="modal-body" style="height: calc(100% - 46px); overflow-y: auto;">
        <kendo-grid [kendoGridBinding]="[selectedClientGroupe]" 
      class=" content-wrap flex-shrink-0 ref-grid" style="height: auto !important;" >
        <ng-template kendoGridToolbarTemplate>
          <div  style="height: 44px;" class="d-flex justify-content-between align-items-center px-2 client-have-no-association-bg">
            <span class="text-white fs-4 k-font-weight-bold">Client Groupe {{selectedAnomalie?.indexOfGood === 1 ? '(GOOD)' :''}}</span>
          </div>
        </ng-template>
        <kendo-grid-column field="codeClientGroupe" title="Code groupe" [width]="90">
          <ng-template kendoGridCellTemplate let-dataItem>
           
            <app-copy-cell [value]="dataItem?.codeClientGroupe"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.raisonSociale"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.nomPharmacien"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="ville.libelle" title="Ville" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="adresse1" title="Adresse" [width]="200"></kendo-grid-column>
        <kendo-grid-column field="localite" title="Localité" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="telephone" title="Téléphone" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-copy-cell [value]="dataItem?.telephone"></app-copy-cell>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>


      <!-- Client Groupe Doublon -->
       <!--  loading card  -->
        <div class="card card-body no-transco-card mt-2 d-flex flex-column justify-content-center align-items-center" *ngIf="isLoadingClientGroupeDoublon">
        <div class="d-flex justify-content-center align-items-center">
          <i class="mdi mdi-loading mdi-spin text-primary fs-1"></i>
        </div>
        <p class="font-weight-600 m-0">Chargement de client groupe doublon...</p>
       </div>

        <kendo-grid [kendoGridBinding]="[clientGroupeDoublon]"  *ngIf="!isLoadingClientGroupeDoublon && clientGroupeDoublon && [AnomalieString.DOUBLON_EXACT, AnomalieString.DOUBLON_PROBABLE].includes(selectedAnomalie?.anomalie)"
          class="content-wrap flex-shrink-0 ref-grid mt-3" style="height: auto !important;" >
        <ng-template kendoGridToolbarTemplate>
          <div  style="height: 44px;" class="d-flex justify-content-between align-items-center px-2 client-have-no-association-bg">
            <span class="text-white fs-4 k-font-weight-bold">Client Groupe Doublon  {{selectedAnomalie?.indexOfGood === 2 ? '(GOOD)' :''}}</span>
          </div>
        </ng-template>
          <kendo-grid-column field="codeClientGroupe" title="Code Groupe Doublon" [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-copy-cell [value]="dataItem?.codeClientGroupe"></app-copy-cell>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="200">
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-copy-cell [value]="dataItem?.raisonSociale"></app-copy-cell>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="200">
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-copy-cell [value]="dataItem?.nomPharmacien"></app-copy-cell>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="ville.libelle" title="Ville" [width]="100"></kendo-grid-column>
          <kendo-grid-column field="adresse1" title="Adresse" [width]="200"></kendo-grid-column>
          <kendo-grid-column field="localite" title="Localité" [width]="100"></kendo-grid-column>
          <kendo-grid-column field="telephone" title="Téléphone" [width]="100">
            <ng-template kendoGridCellTemplate let-dataItem>
              <app-copy-cell [value]="dataItem?.telephone"></app-copy-cell>
            </ng-template>
          </kendo-grid-column>
        </kendo-grid>

        <!-- Difference summary for doublon cases -->
        <div class="alert alert-info mt-3" *ngIf="!isLoadingClientGroupeDoublon && clientGroupeDoublon && [AnomalieString.DOUBLON_EXACT, AnomalieString.DOUBLON_PROBABLE].includes(selectedAnomalie?.anomalie)">
          <div class="d-flex align-items-center">
            <div class="">
              <h5 class="alert-heading">Résumé des différences</h5>
              <p>Voici les différences entre les deux clients qui ont été identifiés comme potentiellement identiques:</p>
            </div>
            <button *ngIf="selectedAnomalie?.indexOfGood === 1 || selectedAnomalie?.indexOfGood === 2" type="button" class="btn btn-dark ms-auto rounded-pill" (click)="processCorrectiveAction()" aria-label="Corriger" >Corriger</button>
          </div>
          
          <div class="table-responsive mt-3">
            <table class="table table-bordered">
              <thead class="table-light">
                <tr>
                  <th>Champ</th>
                  <th>Client Original</th>
                  <th>Client Doublon</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngIf="selectedClientGroupe?.raisonSociale !== clientGroupeDoublon?.raisonSociale">
                  <td class="fw-bold">Raison Sociale</td>
                  <td [innerHTML]="highlightDifferences(selectedClientGroupe?.raisonSociale, clientGroupeDoublon?.raisonSociale)"></td>
                  <td [innerHTML]="highlightDifferences(clientGroupeDoublon?.raisonSociale, selectedClientGroupe?.raisonSociale)"></td>
                </tr>
                <tr *ngIf="selectedClientGroupe?.nomPharmacien !== clientGroupeDoublon?.nomPharmacien">
                  <td class="fw-bold">Nom Pharmacien</td>
                  <td [innerHTML]="highlightDifferences(selectedClientGroupe?.nomPharmacien, clientGroupeDoublon?.nomPharmacien)"></td>
                  <td [innerHTML]="highlightDifferences(clientGroupeDoublon?.nomPharmacien, selectedClientGroupe?.nomPharmacien)"></td>
                </tr>
                <tr *ngIf="selectedClientGroupe?.ville?.libelle !== clientGroupeDoublon?.ville?.libelle">
                  <td class="fw-bold">Ville</td>
                  <td [innerHTML]="highlightDifferences(selectedClientGroupe?.ville?.libelle, clientGroupeDoublon?.ville?.libelle)"></td>
                  <td [innerHTML]="highlightDifferences(clientGroupeDoublon?.ville?.libelle, selectedClientGroupe?.ville?.libelle)"></td>
                </tr>
                <tr *ngIf="selectedClientGroupe?.adresse1 !== clientGroupeDoublon?.adresse1">
                  <td class="fw-bold">Adresse</td>
                  <td [innerHTML]="highlightDifferences(selectedClientGroupe?.adresse1, clientGroupeDoublon?.adresse1)"></td>
                  <td [innerHTML]="highlightDifferences(clientGroupeDoublon?.adresse1, selectedClientGroupe?.adresse1)"></td>
                </tr>
                <tr *ngIf="selectedClientGroupe?.localite !== clientGroupeDoublon?.localite">
                  <td class="fw-bold">Localité</td>
                  <td [innerHTML]="highlightDifferences(selectedClientGroupe?.localite, clientGroupeDoublon?.localite)"></td>
                  <td [innerHTML]="highlightDifferences(clientGroupeDoublon?.localite, selectedClientGroupe?.localite)"></td>
                </tr>
                <tr *ngIf="selectedClientGroupe?.telephone !== clientGroupeDoublon?.telephone">
                  <td class="fw-bold">Téléphone</td>
                  <td [innerHTML]="highlightDifferences(selectedClientGroupe?.telephone, clientGroupeDoublon?.telephone)"></td>
                  <td [innerHTML]="highlightDifferences(clientGroupeDoublon?.telephone, selectedClientGroupe?.telephone)"></td>
                </tr>
                <tr *ngIf="selectedClientGroupe?.classification !== clientGroupeDoublon?.classification">
                  <td class="fw-bold">Classification</td>
                  <td [innerHTML]="highlightDifferences(selectedClientGroupe?.classification, clientGroupeDoublon?.classification)"></td>
                  <td [innerHTML]="highlightDifferences(clientGroupeDoublon?.classification, selectedClientGroupe?.classification)"></td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div class="mt-2">
            <strong>Score de correspondance: </strong>
            <span class="badge bg-primary">{{selectedAnomalie?.pourcentageMatching}}%</span>
          </div>
        </div>

        <!-- Anomaly type-specific content -->
        <div class="mt-4" [ngSwitch]="selectedAnomalie?.anomalie" *ngIf="!isLoadingClientGroupeDoublon && selectedAnomalie">
          <!-- Doublon cases are already handled above with the grid -->
          
          <!-- NOM_CAR_1 case -->
          <div *ngSwitchCase="AnomalieString.NOM_CAR_1" class="alert alert-warning">
            <h5 class="alert-heading">Anomalie détectée: Un mot de 1 caractère</h5>
            <p>Le nom du client ou le nom du pharmacien contient un mot composé d'un seul caractère, ce qui peut indiquer une saisie incomplète ou incorrecte.</p>
            
            <div class="mt-2" *ngIf="hasOneCharWord(selectedClientGroupe?.raisonSociale)">
              <strong>Raison sociale: </strong> 
              <span [innerHTML]="highlightOneCharWord(selectedClientGroupe?.raisonSociale)"></span>
            </div>
            <div class="mt-2" *ngIf="hasOneCharWord(selectedClientGroupe?.nomPharmacien)">
              <strong>Nom du pharmacien: </strong> 
              <span [innerHTML]="highlightOneCharWord(selectedClientGroupe?.nomPharmacien)"></span>
            </div>
          </div>
          
          <!-- NOM_TROP_COURT case -->
          <div *ngSwitchCase="AnomalieString.NOM_TROP_COURT" class="alert alert-warning">
            <h5 class="alert-heading">Anomalie détectée: Nom trop court</h5>
            <p>Le nom du client ou le nom du pharmacien est trop court, ce qui peut indiquer une saisie incomplète.</p>

            <!-- Editable Raison Sociale -->
            <div class="mt-2 inline-edit-container" *ngIf="isNameTooShort(selectedClientGroupe?.raisonSociale)">
              <strong>Raison sociale: </strong>
              <div class="d-inline-flex align-items-center edit-transition">
                <!-- Display mode -->
                <span *ngIf="!isEditingRaisonSociale"
                      class="text-danger fw-bold me-2 field-display"
                      [title]="selectedClientGroupe?.raisonSociale">
                  {{selectedClientGroupe?.raisonSociale}}
                </span>

                <!-- Edit mode -->
                <div *ngIf="isEditingRaisonSociale" class="d-inline-flex align-items-center">
                  <input type="text"
                         id="raisonSocialeInput"
                         class="form-control form-control-sm me-2"
                         style="width: 250px;"
                         [(ngModel)]="editedRaisonSociale"
                         (keydown.enter)="onEnterKey('raisonSociale')"
                         (keydown.escape)="onEscapeKey('raisonSociale')"
                         [disabled]="isSavingEdit"
                         placeholder="Saisir la raison sociale"
                         #raisonSocialeInput>

                  <button class="btn btn-sm btn-success me-1"
                          (click)="saveRaisonSociale()"
                          [disabled]="isSavingEdit || !editedRaisonSociale.trim()"
                          title="Sauvegarder (Entrée)">
                    <i class="mdi mdi-check" *ngIf="!isSavingEdit"></i>
                    <span class="spinner-border spinner-border-sm" *ngIf="isSavingEdit"></span>
                  </button>

                  <button class="btn btn-sm btn-secondary"
                          (click)="cancelEditingRaisonSociale()"
                          [disabled]="isSavingEdit"
                          title="Annuler (Échap)">
                    <i class="mdi mdi-close"></i>
                  </button>
                </div>

                <!-- Edit button -->
                <button *ngIf="!isEditingRaisonSociale"
                        class="btn btn-sm btn-outline-primary ms-2"
                        (click)="startEditingRaisonSociale()"
                        title="Modifier directement">
                  <i class="mdi mdi-pencil"></i>
                </button>
              </div>
            </div>

            <!-- Editable Nom Pharmacien -->
            <div class="mt-2 inline-edit-container" *ngIf="isNameTooShort(selectedClientGroupe?.nomPharmacien)">
              <strong>Nom du pharmacien: </strong>
              <div class="d-inline-flex align-items-center edit-transition">
                <!-- Display mode -->
                <span *ngIf="!isEditingNomPharmacien"
                      class="text-danger fw-bold me-2 field-display"
                      [title]="selectedClientGroupe?.nomPharmacien">
                  {{selectedClientGroupe?.nomPharmacien}}
                </span>

                <!-- Edit mode -->
                <div *ngIf="isEditingNomPharmacien" class="d-inline-flex align-items-center">
                  <input type="text"
                         id="nomPharmacienInput"
                         class="form-control form-control-sm me-2"
                         style="width: 250px;"
                         [(ngModel)]="editedNomPharmacien"
                         (keydown.enter)="onEnterKey('nomPharmacien')"
                         (keydown.escape)="onEscapeKey('nomPharmacien')"
                         [disabled]="isSavingEdit"
                         placeholder="Saisir le nom du pharmacien"
                         #nomPharmacienInput>

                  <button class="btn btn-sm btn-success me-1"
                          (click)="saveNomPharmacien()"
                          [disabled]="isSavingEdit || !editedNomPharmacien.trim()"
                          title="Sauvegarder (Entrée)">
                    <i class="mdi mdi-check" *ngIf="!isSavingEdit"></i>
                    <span class="spinner-border spinner-border-sm" *ngIf="isSavingEdit"></span>
                  </button>

                  <button class="btn btn-sm btn-secondary"
                          (click)="cancelEditingNomPharmacien()"
                          [disabled]="isSavingEdit"
                          title="Annuler (Échap)">
                    <i class="mdi mdi-close"></i>
                  </button>
                </div>

                <!-- Edit button -->
                <button *ngIf="!isEditingNomPharmacien"
                        class="btn btn-sm btn-outline-primary ms-2"
                        (click)="startEditingNomPharmacien()"
                        title="Modifier directement">
                  <i class="mdi mdi-pencil"></i>
                </button>
              </div>
            </div>

            <!-- Linked Sites Grid for Field Selection -->
            <div class="mt-4" *ngIf="!isLoadingLinkedSitesForAnomaly && linkedSitesForAnomaly.total > 0">
              <h6 class="mb-3">
                <i class="mdi mdi-link-variant me-2"></i>
                Sites Liés - Sélectionner des valeurs pour corriger l'anomalie
                <span class="badge bg-primary ms-2">{{linkedSitesForAnomaly.total}}</span>
              </h6>

              <kendo-grid [data]="linkedSitesForAnomaly"
                style="height: 400px;"
                [pageable]="true"
                [pageSize]="linkedSitesForAnomalyNavigation.pageSize"
                [skip]="linkedSitesForAnomalyNavigation.skip"
                (pageChange)="linkedSitesForAnomalyPageChange($event.skip)"
                [sortable]="true"
                [sort]="linkedSitesForAnomalySort"
                (sortChange)="linkedSitesForAnomalySortChange($event)"
                class="content-wrap client-have-association-grid compact-grid linked-sites-anomaly-grid">

                <kendo-grid-column field="cliCode" title="Code" [width]="80">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <app-copy-cell [value]="dataItem.cliCode"></app-copy-cell>
                  </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="cliRaiSoc" title="Raison Sociale" [width]="200" headerClass="overflow-visible" class="overflow-visible">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <div class="d-flex justify-content-between align-items-center field-value-cell">
                      <span class="me-1" [title]="dataItem.cliRaiSoc">{{dataItem.cliRaiSoc}}</span>
                      <div ngbDropdown class="d-inline-block">
                        <button class="btn btn-sm btn-outline-primary" ngbDropdownToggle
                          [disabled]="!dataItem.cliRaiSoc || dataItem.cliRaiSoc.trim().length <= 3">
                          <i class="mdi mdi-menu-down"></i>
                        </button>
                        <div ngbDropdownMenu class="dropdown-menu-actions">
                          <button ngbDropdownItem
                            (click)="updateClientGroupeFromSite('raisonSociale', dataItem.cliRaiSoc, dataItem.cliRaiSoc)"
                            [disabled]="!dataItem.cliRaiSoc || dataItem.cliRaiSoc.trim().length <= 3">
                            <i class="mdi mdi-arrow-right me-2"></i>
                            Utiliser comme Raison Sociale
                          </button>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="cliNomPhar" title="Nom Pharmacien" [width]="200" headerClass="overflow-visible" class="overflow-visible">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <div class="d-flex justify-content-between align-items-center field-value-cell">
                      <span class="me-2" [title]="dataItem.cliNomPhar">{{dataItem.cliNomPhar}}</span>
                      <div ngbDropdown class="d-inline-block">
                        <button class="btn btn-sm btn-outline-success" ngbDropdownToggle
                          [disabled]="!dataItem.cliNomPhar || dataItem.cliNomPhar.trim().length <= 3">
                          <i class="mdi mdi-menu-down"></i>
                        </button>
                        <div ngbDropdownMenu class="dropdown-menu-actions">
                          <button ngbDropdownItem
                            (click)="updateClientGroupeFromSite('nomPharmacien', dataItem.cliNomPhar, dataItem.cliRaiSoc)"
                            [disabled]="!dataItem.cliNomPhar || dataItem.cliNomPhar.trim().length <= 3">
                            <i class="mdi mdi-arrow-right me-2"></i>
                            Utiliser comme Nom Pharmacien
                          </button>
                          <button ngbDropdownItem
                            (click)="updateClientGroupeFromSite('raisonSociale', dataItem.cliNomPhar, dataItem.cliRaiSoc)"
                            [disabled]="!dataItem.cliNomPhar || dataItem.cliNomPhar.trim().length <= 3">
                            <i class="mdi mdi-arrow-right me-2"></i>
                            Utiliser comme Raison Sociale
                          </button>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="cliVille" title="Ville" [width]="120">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    {{dataItem.cliVille}}
                  </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="cliTel" title="Téléphone" [width]="120">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <app-copy-cell [value]="dataItem.cliTel"></app-copy-cell>
                  </ng-template>
                </kendo-grid-column>

                <kendo-grid-column field="estActifSite" title="Actif" [width]="80">
                  <ng-template kendoGridCellTemplate let-dataItem>
                    <span class="badge" [ngClass]="dataItem.estActifSite ? 'bg-success' : 'bg-danger'">
                      {{dataItem.estActifSite ? 'Oui' : 'Non'}}
                    </span>
                  </ng-template>
                </kendo-grid-column>
              </kendo-grid>
            </div>

            <!-- Loading state for linked sites -->
            <div class="mt-4 text-center" *ngIf="isLoadingLinkedSitesForAnomaly">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Chargement des sites liés...</span>
              </div>
              <p class="mt-2">Chargement des sites liés...</p>
            </div>

            <!-- No linked sites message -->
            <div class="mt-4 alert alert-info" *ngIf="!isLoadingLinkedSitesForAnomaly && linkedSitesForAnomaly.total === 0">
              <i class="mdi mdi-information me-2"></i>
              Aucun site lié trouvé pour ce client groupe.
            </div>

            <!-- Full Client Group Editing Button -->
            <div class="mt-4 border-top pt-4">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">
                  <i class="mdi mdi-account-edit me-2"></i>
                  Édition complète du Client Groupe
                </h6>
                <button class="btn btn-primary btn-sm"
                        (click)="startEditingFullClientGroupe()"
                        [disabled]="isSavingEdit">
                  <i class="mdi mdi-pencil me-1"></i>
                  Modifier tout l'objet
                </button>
              </div>
            </div>
          </div>
          
          <!-- RAISON_VILLE case -->
          <div *ngSwitchCase="AnomalieString.RAISON_VILLE" class="alert alert-warning">
            <h5 class="alert-heading">Anomalie détectée: Raison sociale contient une ville</h5>
            <p>La raison sociale contient le nom d'une ville, ce qui peut être inapproprié.</p>
            <div class="mt-2">
              <strong>Raison sociale: </strong> 
              <span [innerHTML]="highlightCityInName(selectedClientGroupe?.raisonSociale, selectedClientGroupe?.ville?.libelle)"></span>
            </div>
            <div class="mt-2">
              <strong>Ville de Client : </strong> {{selectedClientGroupe?.ville?.libelle}}
            </div>
          </div>
          
          <!-- CHAMP_MANQUANT case -->
          <div *ngSwitchCase="AnomalieString.CHAMP_MANQUANT" class="alert alert-danger">
            <h5 class="alert-heading">Anomalie détectée: Champ manquant</h5>
            <p>Un ou plusieurs champs importants sont manquants dans les informations du client.</p>
            <div class="mt-2" *ngIf="!selectedClientGroupe?.raisonSociale">
              <strong class="text-danger">Raison sociale manquante</strong>
            </div>
            <div class="mt-2" *ngIf="!selectedClientGroupe?.nomPharmacien">
              <strong class="text-danger">Nom du pharmacien manquant</strong>
            </div>
            <div class="mt-2" *ngIf="!selectedClientGroupe?.ville?.libelle">
              <strong class="text-danger">Ville manquante</strong>
            </div>
            <div class="mt-2" *ngIf="!selectedClientGroupe?.adresse1">
              <strong class="text-danger">Adresse manquante</strong>
            </div>
          </div>
          
          <!-- Default case -->
           <div *ngSwitchDefault>
             <div class="alert alert-info" *ngIf="selectedAnomalie?.anomalie !== AnomalieString.DOUBLON_EXACT && selectedAnomalie?.anomalie !== AnomalieString.DOUBLON_PROBABLE">
                  <p class="m-0">Aucune information supplémentaire disponible pour cette anomalie.</p>
              </div>
           </div>
        </div>
      </div>
  </app-custom-drawer>


  <!-- Association Drawer -->
<app-custom-drawer 
  [isOpen]="isAssocitionDrawerOpen" 
  [width]="'80%'" 
  [title]="'Consultation Client Groupe et leurs Associés'"
  (isOpenChange)="isAssocitionDrawerOpen = $event">
  
  <div drawer-body *ngIf="!isLoadingClientGroupeDoublon && !isConsultDrawerOpen && (clientDoublentConsulted || selectedClientGroupe)">
     <app-client-groupe-linked-sites [clientGroupeGridTitle]="clientDoublentConsulted ? 'Client Groupe Doublon' : 'Client Groupe'" [clientGroupe]="clientDoublentConsulted ? clientGroupeDoublon : selectedClientGroupe"></app-client-groupe-linked-sites>
  </div>
</app-custom-drawer>

<!-- Full Client Group Edit Modal -->
<ng-template #fullEditModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">
      <i class="mdi mdi-account-edit me-2"></i>
      Édition complète du Client Groupe
    </h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
    <form [formGroup]="clientGroupeForm" (ngSubmit)="saveFullClientGroupe(); modal.close()">
      <div class="row">
        <!-- Basic Information -->
        <div class="col-md-6 mb-3">
          <label for="modalRaisonSociale" class="form-label">Raison Sociale <span class="text-danger">*</span></label>
          <input id="modalRaisonSociale"
                 formControlName="raisonSociale"
                 class="form-control"
                 [class.is-invalid]="formSubmitted && clientGroupeForm.get('raisonSociale')?.invalid">
          <div *ngIf="formSubmitted && clientGroupeForm.get('raisonSociale')?.invalid" class="invalid-feedback">
            Raison Sociale est obligatoire.
          </div>
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalNomPharmacien" class="form-label">Nom du Pharmacien <span class="text-danger">*</span></label>
          <input id="modalNomPharmacien"
                 formControlName="nomPharmacien"
                 class="form-control"
                 [class.is-invalid]="formSubmitted && clientGroupeForm.get('nomPharmacien')?.invalid">
          <div *ngIf="formSubmitted && clientGroupeForm.get('nomPharmacien')?.invalid" class="invalid-feedback">
            Nom du Pharmacien est obligatoire.
          </div>
        </div>

        <!-- Address Information -->
        <div class="col-md-6 mb-3">
          <label for="modalAdresse1" class="form-label">Adresse <span class="text-danger">*</span></label>
          <input id="modalAdresse1"
                 formControlName="adresse1"
                 class="form-control"
                 [class.is-invalid]="formSubmitted && clientGroupeForm.get('adresse1')?.invalid">
          <div *ngIf="formSubmitted && clientGroupeForm.get('adresse1')?.invalid" class="invalid-feedback">
            Adresse est obligatoire.
          </div>
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalAdresse2" class="form-label">Complément d'adresse</label>
          <input id="modalAdresse2"
                 formControlName="adresse2"
                 class="form-control">
        </div>

        <!-- Location Information -->
        <div class="col-md-6 mb-3">
          <label for="modalLocalite" class="form-label">Localité</label>
          <input id="modalLocalite"
                 formControlName="localite"
                 class="form-control"
                 [ngbTypeahead]="searchLocaliteTypeahead"
                 [resultFormatter]="formatterLocalite"
                 [inputFormatter]="formatterLocalite">
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalVille" class="form-label">Ville <span class="text-danger">*</span></label>
          <select id="modalVille"
                  formControlName="ville"
                  class="form-select"
                  [class.is-invalid]="formSubmitted && clientGroupeForm.get('ville')?.invalid">
            <option [ngValue]="null">Sélectionner une ville</option>
            <option *ngFor="let ville of cities" [ngValue]="ville">{{ville.libelle}}</option>
          </select>
          <div *ngIf="formSubmitted && clientGroupeForm.get('ville')?.invalid" class="invalid-feedback">
            Ville est obligatoire.
          </div>
        </div>

        <!-- Contact Information -->
        <div class="col-md-6 mb-3">
          <label for="modalTelephone" class="form-label">Téléphone <span class="text-danger">*</span></label>
          <input id="modalTelephone"
                 formControlName="telephone"
                 class="form-control"
                 [class.is-invalid]="formSubmitted && clientGroupeForm.get('telephone')?.invalid">
          <div *ngIf="formSubmitted && clientGroupeForm.get('telephone')?.invalid" class="invalid-feedback">
            Téléphone est obligatoire.
          </div>
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalTelephone2" class="form-label">Téléphone 2</label>
          <input id="modalTelephone2"
                 formControlName="telephone2"
                 class="form-control">
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalGsm" class="form-label">GSM</label>
          <input id="modalGsm"
                 formControlName="gsm"
                 class="form-control">
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalWhatsapp" class="form-label">WhatsApp</label>
          <input id="modalWhatsapp"
                 formControlName="whatsapp"
                 class="form-control">
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalEmail" class="form-label">Email</label>
          <input id="modalEmail"
                 type="email"
                 formControlName="email"
                 class="form-control"
                 [class.is-invalid]="formSubmitted && clientGroupeForm.get('email')?.invalid">
          <div *ngIf="formSubmitted && clientGroupeForm.get('email')?.invalid" class="invalid-feedback">
            Format d'email invalide.
          </div>
        </div>

        <!-- Business Information -->
        <div class="col-md-6 mb-3">
          <label for="modalIce" class="form-label">ICE</label>
          <input id="modalIce"
                 formControlName="ice"
                 class="form-control">
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalPatente" class="form-label">Patente</label>
          <input id="modalPatente"
                 formControlName="patente"
                 class="form-control">
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalInpe" class="form-label">INPE</label>
          <input id="modalInpe"
                 formControlName="inpe"
                 class="form-control">
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalClassification" class="form-label">Classification</label>
          <input id="modalClassification"
                 formControlName="classification"
                 class="form-control">
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalTag" class="form-label">Tag</label>
          <input id="modalTag"
                 formControlName="tag"
                 class="form-control">
        </div>

        <div class="col-md-6 mb-3">
          <label for="modalSegment" class="form-label">Segment</label>
          <select id="modalSegment"
                  formControlName="segment"
                  class="form-select">
            <option *ngFor="let option of segmentOptions" [value]="option.value">{{option.label}}</option>
          </select>
        </div>
      </div>
    </form>
  </div>

  <div class="modal-footer">
    <button type="button"
            class="btn btn-secondary"
            (click)="modal.dismiss()"
            [disabled]="isSavingEdit">
      <i class="mdi mdi-close me-1"></i>
      Annuler
    </button>
    <button type="button"
            class="btn btn-primary"
            (click)="saveFullClientGroupe(); modal.close()"
            [disabled]="isSavingEdit">
      <span *ngIf="isSavingEdit" class="spinner-border spinner-border-sm me-2" role="status"></span>
      <i *ngIf="!isSavingEdit" class="mdi mdi-content-save me-1"></i>
      {{isSavingEdit ? 'Sauvegarde...' : 'Sauvegarder'}}
    </button>
  </div>
</ng-template>
