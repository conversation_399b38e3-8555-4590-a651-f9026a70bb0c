import { AuthService } from 'src/app/shared/services/auth.service';
import { MenuItem } from '../models/menu.model';
import { environment } from 'src/environments/environment';
const ROLE_AIDEPHARMACIEN = 'ROLE_AIDEPHARMACIEN';
const ROLE_PHARMACIEN = 'ROLE_PHARMACIEN';
const ROLE_SUPERADMIN = 'ROLE_SUPERADMIN';


function getPrincipal() {
    let principalString = localStorage.getItem('principal');
    if (principalString)
        return JSON.parse(principalString);
    return null;
}
function labelSuggestProduit() {

    if (getPrincipal() && getPrincipal()?.mainAuthority == "ROLE_PHARMACIEN")
        return "Mes suggestion"
    return "Liste des suggestion"

}
export const MENU: MenuItem[] = [


    // { key: 'fastlink-saivente', label: 'Liste des Commandes', style: 'bg-item bg-vente ', icon: "uil-truck",  link: '/references-produits/achat/commandes', authorities: ["CREATION_VENTE"], collapsed: true, },
    {
        label: 'Accueil',
        icon: ' dripicons-home',
        link: '/dashboard',
        style: 'bg-item bg-achat',
        collapsed: true,
        parentKey: null,
      },
    {
        label: 'Réf. Produits',
        key: 'produits',
        icon: 'mdi mdi-pill',
        collapsed: true,
        style: 'bg-item bg-achat',
        children: [
            { key: 'prd-ite', label: 'Produits des sites', link: '/references/ref-produits/produits/produits-sites', parentKey: 'produits'}, 
            { key: 'prd-grp', label: 'Produits du Groupe', link: '/references/ref-produits/produits/produits-groupes', parentKey: 'produits'}, 
            { key: 'prd-frn', label: 'Produit Winplus', link: '/references/ref-produits/produits/produit-winplus', parentKey: 'produits'}, 
            { key: 'prd-win', label: 'Envoie Produits', link: '/references/ref-produits/produits/produits-envoie', parentKey: 'produits'}, 
            // { key: 'reception', label: 'link 2', link: '/references-produits/produits/receptionbl', parentKey: 'produits'}, 
            // { key: 'fact', label: 'link 3', link: '/references-produits/produits/facturation', parentKey: 'produits'},
        ],
        // authorities: ['MENU_ACHAT', 'ACHAT']
    },
    {
        label: 'Réf. Clients',
        key: 'clients',
        icon: 'mdi mdi-account-multiple',
        collapsed: true,
        style: 'bg-item bg-achat',
        children: [
            // { key: 'winclient-acceuil', label: 'Acceuil', link: '/references/ref-clients', parentKey: 'clients', },
            { key: 'client-groupe', label: 'Clients Groupe', link: '/references/ref-clients/client-groupe', parentKey: 'clients', },
            { key: 'client-sites', label: 'Clients Sites', link: '/references/ref-clients/client-sites', parentKey: 'clients', },
            { key: 'winclient-ville', label: 'Gestion Ville', link: '/references/ref-clients/villes', parentKey: 'clients', },
            { key: 'winclient-region', label: 'Gestion Region', link: '/references/ref-clients/regions', parentKey: 'clients', },
            { key: 'winclient-sites', label: 'Gestion Sites', link: '/references/ref-clients/sites', parentKey: 'clients', },
            { key: 'winclient-localite', label: 'Gestion Localite', link: '/references/ref-clients/localites', parentKey: 'clients', },
            { key: 'winclient-province', label: 'Gestion Province', link: '/references/ref-clients/provinces', parentKey: 'clients', },
            // { key: 'reception', label: 'link 2', link: '/references-clients/clients/receptionbl', parentKey: 'clients' }, 
            // { key: 'fact', label: 'link 3', link: '/references-clients/clients/facturation', parentKey: 'clients' },
        ],
        // authorities: ['MENU_ACHAT', 'ACHAT']
    },

     {
        label: 'Winpharm Clients',
        authorities: [ROLE_SUPERADMIN],    
        key: 'winpharm-clients',
        icon: 'mdi mdi-account-multiple',
        collapsed: true,
        style: 'bg-item bg-achat',
     },

     {
        label: 'Platform Config',
        authorities: [ROLE_SUPERADMIN],    
        key: 'platform-config',
        icon: 'mdi mdi-cog',
        collapsed: true,
        style: 'bg-item bg-achat',
        children: [
            { key: 'conf-users', label: 'Gestion Utilisateurs', link: '/references/platform-config/gestion-utilisateur', parentKey: 'platform-config'}, 
            { key: 'conf-batches', label: 'Exécution Batchs', link: '/references/platform-config/execution-batch', parentKey: 'platform-config'}, 
         ],
     },
    // {
    //     label: "Guide",
    //     link: '/guides/liste',
    //     icon: 'mdi mdi-help-circle',
    //     style: 'bg-item bg-achat',
    //     collapsed: true,
    //     parentKey: null,
    //   },
    //   {
    //     label: 'Se Déconnecter',
    //     icon: 'mdi mdi-power',
    //     link: '/auth/login',
    //     style: 'bg-item bg-achat test logout-link',
    //     collapsed: true,
    //     parentKey: null,
    //   },

    // {
    //     label: 'Config Plateforme',
    //     key: 'Configuration-plateforme',
    //     collapsed: true,
    //     icon: 'uil-cog ',
    //     style: 'bg-item bg-config-platform',
    //     link: "/references-produits/config-plateforme",
    //     authorities: [ROLE_SUPERADMIN, ROLE_PHARMACIEN]
    // },
    

];



