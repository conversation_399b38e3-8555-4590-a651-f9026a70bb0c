<!-- start page title -->
<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">Gestion Utilisateurs</h4>
      <div class="d-flex flex-wrap justify-content-end ">
          <button type="button" class="btn btn-primary"   (click)="openCreateUser()"
         >
         <i class="mdi mdi-plus"></i>
          Nouveau
        </button>
      </div>
    </div>
  </div>
</div>


<kendo-grid [data]="users" 
style="height: calc(100vh - 130px);border-radius: 10px;" 
class="winClient-stats-grid ref-grid custom-sort-grid"
 [pageable]="true" [pageSize]="navigation.pageSize" [skip]="navigation.skip" 
 [sortable]="true"
 [sort]="sort"
 (sortChange)="sortChange($event)"
>
<kendo-grid-column field="username" title="Username" class="text-start" [headerClass]="'text-start'">
   <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
    </ng-template>
</kendo-grid-column>
  <kendo-grid-column  title="Nom Complet" class="text-start" [headerClass]="'text-start'">
    <ng-template kendoGridCellTemplate let-dataItem>
      {{dataItem.lastname}} {{dataItem.firstname}} 
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="email" title="Email" class="text-start" [headerClass]="'text-start'">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="role" title="Role" class="text-start" [headerClass]="'text-start'">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      {{dataItem.role.label}}
    </ng-template>
  </kendo-grid-column>
 
  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
  let-total="total">
  <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage" [allowPageSizes]="false"
    [navigation]="navigation" style="width: 100%;"
    (pageChange)="pageChange($event)"></wph-grid-custom-pager>
</ng-template>
</kendo-grid>

<!-- Create User Modal -->
<ng-template #createUserModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">Créer un nouvel utilisateur</h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="onCancelCreateUser()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <form [formGroup]="createUserForm" (ngSubmit)="onSubmitCreateUser()">
      <div class="row">
        <!-- Email -->
        <div class="col-md-6 mb-3">
          <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
          <input
            type="email"
            class="form-control"
            id="email"
            formControlName="email"
             placeholder="Entrez l'email">
          
        </div>

        <!-- Username -->
        <div class="col-md-6 mb-3">
          <label for="username" class="form-label">Nom d'utilisateur <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            id="username"
            formControlName="username"
             placeholder="Entrez le nom d'utilisateur">
        
        </div>
      </div>

      <div class="row">
        <!-- First Name -->
        <div class="col-md-6 mb-3">
          <label for="firstname" class="form-label">Prénom <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            id="firstname"
            formControlName="firstname"
             placeholder="Entrez le prénom">
         
        </div>

        <!-- Last Name -->
        <div class="col-md-6 mb-3">
          <label for="lastname" class="form-label">Nom <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            id="lastname"
            formControlName="lastname"
             placeholder="Entrez le nom">
       
        </div>
      </div>

      <div class="row">
        <!-- Password -->
        <div class="col-md-6 mb-3">
          <label for="newPassword" class="form-label">Mot de passe <span class="text-danger">*</span></label>
          <input
            type="password"
            class="form-control"
            id="newPassword"
            formControlName="newPassword"
             placeholder="Entrez le mot de passe">
     
        </div>

        <!-- Role -->
        <div class="col-md-6 mb-3">
          <label for="role" class="form-label">Rôle <span class="text-danger">*</span></label>
          <select
            class="form-select"
            id="role"
            formControlName="role"
           >
            <option value="">Sélectionnez un rôle</option>
            <option *ngFor="let role of roles" [ngValue]="role">{{role.label}}</option>
          </select>
        </div>
      </div>
    </form>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="onCancelCreateUser()">
      <i class="mdi mdi-close"></i> Annuler
    </button>
    <button type="button" class="btn btn-primary" (click)="onSubmitCreateUser()" [disabled]="!createUserForm.valid">
      <i class="mdi mdi-check"></i> Créer
    </button>
  </div>
</ng-template>

