<!-- start page title -->
<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">Gestion Utilisateurs</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
        <button type="button" class="btn btn-outline-secondary position-relative" (click)="openSearchDrawer()">
          <i class="mdi mdi-magnify"></i>
          Rechercher
          <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-primary"
                *ngIf="hasActiveFilters()">
            <span class="visually-hidden">Filtres actifs</span>
          </span>
        </button>
        <button type="button" class="btn btn-primary" (click)="openCreateUser()">
          <i class="mdi mdi-plus"></i>
          Nouveau
        </button>
      </div>
    </div>
  </div>
</div>


<kendo-grid [data]="users"
style="height: calc(100vh - 130px);border-radius: 10px;"
class="winClient-stats-grid ref-grid custom-sort-grid"
 [pageable]="true" [pageSize]="navigation.pageSize" [skip]="navigation.skip"
 [sortable]="true"
 [sort]="sort"
 [rowClass]="rowClassCallback"
 (sortChange)="sortChange($event)"
>
<kendo-grid-column field="username" title="Username" class="text-start" [headerClass]="'text-start'">
   <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
    </ng-template>
</kendo-grid-column>
  <kendo-grid-column  title="Nom Complet" class="text-start" [headerClass]="'text-start'">
    <ng-template kendoGridCellTemplate let-dataItem>
      {{dataItem.lastname}} {{dataItem.firstname}} 
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="email" title="Email" class="text-start" [headerClass]="'text-start'">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
    </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="role" title="Role" class="text-start" [headerClass]="'text-start'">
       <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
    </ng-template>
    <ng-template kendoGridCellTemplate let-dataItem>
      {{dataItem.role.label}}
    </ng-template>
  </kendo-grid-column>

  <!-- Actions Column -->
  <kendo-grid-column title="Actions" [width]="150" [sortable]="false" class="text-center" [headerClass]="'text-center'">
    <ng-template kendoGridCellTemplate let-dataItem>
      <button type="button" class="btn btn-sm btn-outline-primary me-1" (click)="openEditUser(dataItem)" title="Modifier">
        <i class="mdi mdi-pencil"></i>
      </button>
      <button type="button"
              class="btn btn-sm btn-outline-danger"
              (click)="onDeleteUser(dataItem)"
              title="Supprimer"
              *ngIf="dataItem.enabled">
        <i class="mdi mdi-delete"></i>
      </button>
    </ng-template>
  </kendo-grid-column>
 
  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
  let-total="total">
  <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage" [allowPageSizes]="false"
    [navigation]="navigation" style="width: 100%;"
    (pageChange)="pageChange($event)"></wph-grid-custom-pager>
</ng-template>
</kendo-grid>

<!-- Create/Edit User Modal -->
<ng-template #createUserModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
      {{ isEditMode ? 'Modifier l\'utilisateur' : 'Créer un nouvel utilisateur' }}
    </h4>
    <button type="button" class="btn-close" aria-label="Close" (click)="onCancelCreateUser()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>

  <div class="modal-body">
    <form [formGroup]="createUserForm" (ngSubmit)="onSubmitCreateUser()">
      <div class="row">
        <!-- Email -->
        <div class="col-md-6 mb-3">
          <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
          <input
            type="email"
            class="form-control"
            id="email"
            formControlName="email"
             placeholder="Entrez l'email">
          
        </div>

        <!-- Username -->
        <div class="col-md-6 mb-3">
          <label for="username" class="form-label">Nom d'utilisateur <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            id="username"
            formControlName="username"
             placeholder="Entrez le nom d'utilisateur">
        
        </div>
      </div>

      <div class="row">
        <!-- First Name -->
        <div class="col-md-6 mb-3">
          <label for="firstname" class="form-label">Prénom <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            id="firstname"
            formControlName="firstname"
             placeholder="Entrez le prénom">
         
        </div>

        <!-- Last Name -->
        <div class="col-md-6 mb-3">
          <label for="lastname" class="form-label">Nom <span class="text-danger">*</span></label>
          <input
            type="text"
            class="form-control"
            id="lastname"
            formControlName="lastname"
             placeholder="Entrez le nom">
       
        </div>
      </div>

      <div class="row">
        <!-- Password -->
        <div class="col-md-6 mb-3">
          <label for="newPassword" class="form-label">
            Mot de passe
            <span class="text-danger" *ngIf="!isEditMode">*</span>
            <small class="text-muted" *ngIf="isEditMode">(Laisser vide pour ne pas changer)</small>
          </label>
          <input
            type="password"
            class="form-control"
            id="newPassword"
            formControlName="newPassword"
            [placeholder]="isEditMode ? 'Nouveau mot de passe (optionnel)' : 'Entrez le mot de passe'">
        </div>

        <!-- Role -->
        <div class="col-md-6 mb-3">
          <label for="role" class="form-label">Rôle <span class="text-danger">*</span></label>
          <select
            class="form-select"
            id="role"
            formControlName="role"
            [compareWith]="compareRoles"
           >
            <option value="">Sélectionnez un rôle</option>
            <option *ngFor="let role of roles" [ngValue]="role">{{role.label}}</option>
          </select>
        </div>
      </div>
    </form>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="onCancelCreateUser()">
      <i class="mdi mdi-close"></i> Annuler
    </button>
    <button type="button" class="btn btn-primary" (click)="onSubmitCreateUser()" [disabled]="!createUserForm.valid">
      <i class="mdi" [ngClass]="isEditMode ? 'mdi-content-save' : 'mdi-check'"></i>
      {{ isEditMode ? 'Modifier' : 'Créer' }}
    </button>
  </div>
</ng-template>

<!-- Search Drawer -->
<app-custom-drawer
  [isOpen]="isSearchDrawerOpen"
  (isOpenChange)="isSearchDrawerOpen = $event"
  [title]="'Rechercher des utilisateurs'"
  [width]="'500px'">

  <div class="p-3">
    <form [formGroup]="searchForm" (ngSubmit)="onSearchSubmit()">
      <div class="row">
        <!-- Email -->
        <div class="col-12 mb-3">
          <label for="searchEmail" class="form-label">Email</label>
          <input
            type="email"
            class="form-control"
            id="searchEmail"
            formControlName="email"
            placeholder="Rechercher par email">
        </div>

        <!-- Username -->
        <div class="col-12 mb-3">
          <label for="searchUsername" class="form-label">Nom d'utilisateur</label>
          <input
            type="text"
            class="form-control"
            id="searchUsername"
            formControlName="username"
            placeholder="Rechercher par nom d'utilisateur">
        </div>

        <!-- First Name -->
        <div class="col-12 mb-3">
          <label for="searchFirstname" class="form-label">Prénom</label>
          <input
            type="text"
            class="form-control"
            id="searchFirstname"
            formControlName="firstname"
            placeholder="Rechercher par prénom">
        </div>

        <!-- Last Name -->
        <div class="col-12 mb-3">
          <label for="searchLastname" class="form-label">Nom</label>
          <input
            type="text"
            class="form-control"
            id="searchLastname"
            formControlName="lastname"
            placeholder="Rechercher par nom">
        </div>

        <!-- Role -->
        <div class="col-12 mb-3">
          <label for="searchRole" class="form-label">Rôle</label>
          <select
            class="form-select"
            id="searchRole"
            formControlName="role"
            [compareWith]="compareRoles">
            <option value="">Tous les rôles</option>
            <option *ngFor="let role of roles" [ngValue]="role">{{role.label}}</option>
          </select>
        </div>

        <!-- Enabled Status -->
        <div class="col-12 mb-3">
          <label for="searchEnabled" class="form-label">Statut</label>
          <select
            class="form-select"
            id="searchEnabled"
            formControlName="enabled">
            <option value="">Tous les statuts</option>
            <option [value]="true">Actif</option>
            <option [value]="false">Inactif</option>
          </select>
        </div>
      </div>
    </form>
  </div>

  <!-- Footer with action buttons -->
  <div drawer-footer class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="clearSearch()">
      <i class="mdi mdi-close"></i> Effacer
    </button>
    <button type="button" class="btn btn-outline-secondary" (click)="resetSearch()">
      <i class="mdi mdi-refresh"></i> Réinitialiser
    </button>
    <button type="button" class="btn btn-primary" (click)="onSearchSubmit()">
      <i class="mdi mdi-magnify"></i> Rechercher
    </button>
  </div>
</app-custom-drawer>
