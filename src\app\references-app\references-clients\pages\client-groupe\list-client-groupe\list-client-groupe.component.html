
<!-- Filter -->
<app-custom-drawer 
  [isOpen]="isFilterDrawerOpen" 
  [width]="'600px'" 
  [title]="'Filter Client Groupe'"
  (isOpenChange)="isFilterDrawerOpen = $event">
  
  <div  class="p-2" >
      <form id="clientGroupeFilterForm" style="display: contents; flex: 1;height: 100%;" [formGroup]="clientGroupeFilterForm" (ngSubmit)="onFilterSubmit()" appFocusTrap>
      <div class="row">
        <div class="col-6 mb-2">
          <label for="codeClientGroupe" class="form-label">Code Groupe</label>
          <input id="codeClientGroupe" formControlName="codeClientGroupe" class="form-control" id="codeGroupe"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="nomPharmacien" class="form-label">Nom Pharmacien</label>
          <input id="nomPharmacien" formControlName="nomPharmacien" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="raisonSociale" class="form-label">Raison Social</label>
          <input id="raisonSociale" formControlName="raisonSociale" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="ville" class="form-label">Ville</label>
          <input id="ville" formControlName="ville" class="form-control" placeholder="taper ville"   type="search"
          [ngbTypeahead]="searchVilleTypeahead" [inputFormatter]="formatterVille" [resultFormatter]="formatterVille" [editable]="false">
        </div>
        <div class="col-6 mb-2">
          <label for="telephone" class="form-label">Telephone</label>
          <input id="telephone" formControlName="telephone" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="classification" class="form-label">Classification</label>
          <input id="classification" formControlName="classification" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="adress" class="form-label">Adress</label>
          <input id="adress" formControlName="adress" class="form-control"   type="search">
        </div>
        <div class="col-6 mb-2">
          <label for="tag" class="form-label">Tag</label>
          <input id="tag" formControlName="tag" class="form-control"   type="search">
        </div>
        <div class="col-12 mb-2">
          <label for="segment" class="form-label mb-0">Type de Client</label>
          <app-switch [elements]='segmentFilterOptions' name="segment" formControlName="segment" [switchClass]="'info'"></app-switch>
        </div>
          <div class="col-12 d-flex flex-column flex-sm-row align-items-start align-items-sm-center">
              <label for="statut" class="form-label me-2 mb-1 mb-sm-0">Utilisé</label>
              <div class="w-100">
                <app-switch
                  [elements]="[{ value: null, label: 'Tous' },{ value: true, label: 'Oui' }, { value: false, label: 'Non' }]"
                  formControlName="estUtilise"
                  name="statut"
                  [disabled]="false"
                  [switchClass]="'info'">
                </app-switch>
              </div>
            </div>
      </div>
      
    </form>
  </div>
      <div class="modal-footer justify-content-start p-0" drawer-footer >
        <div class="d-flex flex-wrap gap-2 p-1">
          <button form="clientGroupeFilterForm" class="btn btn-primary" tabindex="-1" type="submit">Recherche</button>
          <button class="btn btn-dark" tabindex="-1" type="button" (click)="resetFilters()">Vider</button>
        </div>
      </div>
</app-custom-drawer>


<!-- Create Client Group Drawer -->
<app-custom-drawer 
  [isOpen]="isCreateDrawerOpen" 
  [width]="'80%'" 
  [closeOnBackdropClick]="clientGroupeForm?.dirty ? false : true"
  [closeOnEscKey]="clientGroupeForm?.dirty ? false : true"
  [title]="modalMode === 'EDIT' ? 'Modifier Client Groupe' : modalMode === 'VIEW' ? 'Consulter Client Groupe' : 'Créer Client Groupe'"
  (isOpenChange)="isCreateDrawerOpen = $event">

  <div drawer-body class="p-2">
   <form [formGroup]="clientGroupeForm" id="clientGroupeForm" class="mt-2 flex-grow-1 d-flex flex-column w-100" (ngSubmit)="onSubmit()" appFocusTrap >
     <div class="row">
      <div class="col-8">
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="raisonSociale" class="form-label">Raison Sociale<span class="text-danger">*</span></label>
            <input id="raisonSociale"  formControlName="raisonSociale" class="form-control raison-social-create" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('raisonSociale')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('raisonSociale')?.invalid && formSubmited" class="text-danger">
              Raison Sociale est obligatoire.
            </div>
          </div>
      
          <div class="col-md-6 mb-1">
            <label for="nomDuPharmacien" class="form-label">Nom du Pharmacien<span class="text-danger">*</span></label>
            <input id="nomDuPharmacien" formControlName="nomDuPharmacien" class="form-control" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('nomDuPharmacien')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('nomDuPharmacien')?.invalid && formSubmited" class="text-danger">
              Nom du Pharmacien est obligatoire.
            </div>
          </div>
        </div>
      
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="adresse" class="form-label">Adresse<span class="text-danger">*</span></label>
            <input id="adresse" formControlName="adresse" class="form-control" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('adresse')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('adresse')?.invalid && formSubmited" class="text-danger">
              Adresse est obligatoire.
            </div>
          </div>
      
          <div class="col-md-6 mb-1">
            <label for="adresseComplement" class="form-label">Adresse (Complément)</label>
            <input id="adresseComplement" formControlName="adresseComplement" class="form-control">
          </div>
        </div>
      
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="localite" class="form-label">Localité</label>
            <input [ngbTypeahead]="searchLocaliteTypeahead" class="form-control" id="localite"
            formControlName="localite" [inputFormatter]="formatterLocalite"  type="search"
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('localite')?.invalid}"
            placeholder="taper localité"  [editable]="true"
            [resultFormatter]="formatterLocalite">
          </div>
      
          <div class="col-md-6 mb-1">
            <label for="ville" class="form-label">Ville<span class="text-danger">*</span></label>
            <input [ngbTypeahead]="searchVilleTypeahead" class="form-control" id="localite"
            formControlName="ville" [inputFormatter]="formatterVille" [editable]="false"  type="search"
               [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('ville')?.invalid}"
            placeholder="taper ville"
            [resultFormatter]="formatterVille">
            <div *ngIf="clientGroupeForm.get('ville')?.invalid && formSubmited" class="text-danger">
              Ville est obligatoire.
            </div>
          </div>
        </div>
      
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="telephone" class="form-label">Téléphone<span class="text-danger">*</span></label>
            <input id="telephone" formControlName="telephone" class="form-control" required
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('telephone')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('telephone')?.invalid && formSubmited" class="text-danger">
              Téléphone est obligatoire.
            </div>
          </div>
          <div class="col-md-6 mb-1">
            <label for="email" class="form-label">Email</label>
            <input id="email" formControlName="email" class="form-control"  placeholder="ex: <EMAIL>"
            [ngClass]="{'is-invalid':formSubmited && clientGroupeForm.get('email')?.invalid}"
            >
            <div *ngIf="clientGroupeForm.get('email')?.invalid && formSubmited" class="text-danger">
              Email est non valide.
            </div>
          </div>
          <div class="col-md-6 mb-1">
            <label for="telephone2" class="form-label">Téléphone 2</label>
            <input id="telephone2" formControlName="telephone2" class="form-control"  placeholder="ex: 06 00 00 00 00"
            >
          </div>
          <div class="col-md-6 mb-1">
            <label for="gsm" class="form-label">Gsm</label>
            <input id="gsm" formControlName="gsm" class="form-control" placeholder="ex: 06 00 00 00 00">
          </div>
        </div>
        <!--  -->
        <div class="row">
       
          <div class="col-md-6 mb-1">
            <label for="whatsapp" class="form-label">Whatsapp</label>
            <input id="whatsapp" formControlName="whatsapp" class="form-control"  placeholder="ex: 06 00 00 00 00">
          </div>
          <div class="col-md-6 mb-1">
            <label for="ice" class="form-label">ICE</label>
            <input id="ice" formControlName="ice" class="form-control"  placeholder="ex: 06 00 00 00 00">
          </div>
        </div>
        <!--  -->
        <div class="row">
          <div class="col-md-6 mb-1">
            <label for="inpe" class="form-label">inpe</label>
            <input id="inpe" formControlName="inpe" class="form-control"  minlength="9" maxlength="9"
            >
          </div>
          <div class="col-md-6 mb-1">
            <label for="patente" class="form-label">Patente</label>
            <input id="patente" formControlName="patente" class="form-control" 
            >
          </div>
          <div class="col-12">
              <label for="longitude" class="form-label mb-0">Type de Client</label>
              <app-switch [elements]='segmentOptions' name="segment" formControlName="segment" [switchClass]="'info'"></app-switch>
          </div>
        </div>
      </div>
      <div class="col-4">
           <div class="row">
            <div class="col-6">
              <label for="latitude" class="form-label">Latitude</label>
              <input id="latitude" formControlName="latitude" class="form-control">
            </div>
            <div class="col-6">
              <label for="longitude" class="form-label">Longitude</label>
              <input id="longitude" formControlName="longitude" class="form-control">
            </div>
          </div>
         <div class="row mt-2">
          <div class="map-container" *ngIf="isCreateDrawerOpen">
            <app-map (coords)="onMarkerSelected($event)" [selectedLocation]="{
              latitude: clientGroupeForm.get('latitude').value,
              longitude: clientGroupeForm.get('longitude').value
              }"></app-map>
          </div>
        </div>
      </div>
     </div>

  </form>
  </div>
    <div class="modal-footer mt-auto" *ngIf="modalMode != 'VIEW'" drawer-footer>
    <button type="button" class="btn btn-secondary"  tabindex="-1" (click)="toggleCreateDrawer(false)" >Annuler</button>
    <button type="submit" class="btn btn-primary" form="clientGroupeForm"  tabindex="-1" form="clientGroupeForm">
      {{modalMode == "CREATE" ? "Enregistrer" : "Modifier "}}
    </button>
  </div>
</app-custom-drawer>

<!-- Association Drawer -->
<app-custom-drawer 
  [isOpen]="isAssocitionDrawerOpen" 
  [width]="'80%'" 
  [title]="'Client Site Associés'"
  (isOpenChange)="isAssocitionDrawerOpen = $event">
  
  <div drawer-body>
     <app-client-groupe-linked-sites [clientGroupe]="clickedItem"></app-client-groupe-linked-sites>
  </div>
</app-custom-drawer>









<div class="row rowline mb-2">
  <div class="page-title-box row">
    <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h4 class="page-title ps-2">List Client Groupe</h4>
      <div class="d-flex flex-wrap justify-content-end gap-2">
        <button type="button" class="btn btn-primary"
          (click)="toggleCreateDrawer(true)">
         <i class="mdi mdi-plus"></i>
          Nouveau
        </button>
        <button class="btn btn-dark" (click)="toggleFilterDrawer(true)">
          <i class="mdi mdi-filter"></i>
          Filtrer
        </button>
      </div>
    </div>
  </div>
</div>

<kendo-grid [data]="clientsGroupe"  
style="height: calc(100vh - 130px);border-radius: 10px;" class="winClient-stats-grid ref-grid content-wrap custom-sort-grid"
[pageable]="true"
[pageSize]="30"
[pageSize]="navigation.pageSize" [skip]="navigation.skip"
[rowClass]="rowClass"
[sort]="clientGroupeSort"
[sortable]="true"
(sortChange)="onSortChange($event)"
>
  <kendo-grid-column field="codeClientGroupe" title="Code Groupe" [width]="100">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field" [type]="'numeric'"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="raisonSociale" title="Raison Sociale" [width]="200">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="nomPharmacien" title="Nom Pharmacien" [width]="200">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="adresse1" title="Adresse" [width]="200" [sortable]="false"></kendo-grid-column>
  <kendo-grid-column field="localite" title="Localité" [width]="100">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="ville.libelle" title="Ville" [width]="100" [sortable]="false"></kendo-grid-column>
  <kendo-grid-column field="telephone" title="Telephone" [width]="100">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="patente" title="Patente" [width]="100">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
  </kendo-grid-column>
  <kendo-grid-column field="classification" title="Classification" [width]="100">
      <ng-template kendoGridHeaderTemplate  let-dataItem let-column>
        <app-grid-sort-header [title]="column.title"  
        [active]="navigation.sortField === column.field"
        [direction]="navigation.sortMethod"></app-grid-sort-header>
  </ng-template>
  </kendo-grid-column>
  <kendo-grid-column title="Actions" [width]="100">
    <ng-template kendoGridCellTemplate let-dataItem>
      <app-action-icon [icon]="'magnify'"   appTooltip="Consulter Client groupe et Client Sites Associés"  [extendClass]="'circle-lg'" (click)="openAssociationDrawer(dataItem)"></app-action-icon>
      <app-action-icon [icon]="'eye'" [backgroundColor]="'light'"  appTooltip="Consulter Client groupe" [extendClass]="'circle-lg'" (click)="onView(dataItem)"></app-action-icon>
      <app-action-icon [icon]="'pencil'" [backgroundColor]="'success'"  appTooltip="Modifier Client groupe" [extendClass]="'circle-lg'" (click)="onEdit(dataItem)"></app-action-icon>
      <app-action-icon  *ngIf="!dataItem?.dateExclusion" [icon]="'close'" [backgroundColor]="'danger'"  appTooltip="Supprimer Client groupe" [extendClass]="'circle-lg'" (click)="onDeleteClientGroupe(dataItem)"></app-action-icon>
    </ng-template>
  </kendo-grid-column>
  <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
  let-total="total">
  <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage" [allowPageSizes]="true"
    [navigation]="navigation" style="width: 100%;"
    (pageChange)="pageChange($event)"></wph-grid-custom-pager>
</ng-template>
</kendo-grid>