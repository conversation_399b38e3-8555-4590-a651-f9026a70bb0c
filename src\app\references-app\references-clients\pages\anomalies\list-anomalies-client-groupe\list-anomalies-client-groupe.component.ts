import { WinclientVilleService } from './../../../Services/winclient.ville.service';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { WinclientAnomaliesService } from '../../../Services/winclient.anomalies.service';
import { CellClickEvent, CellCloseEvent, GridDataResult, RowClassArgs } from '@progress/kendo-angular-grid';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { AlertService } from 'src/app/shared/services/alert.service';
import { Anomalie, AnomalieCriteria, AnomalieStringEnum, AnomalieType, StatutAnomalie } from '../../../models/anomalie.model';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ClientGroupeService } from '../../../Services/winclient.clientGroupe.service';
import { ClientGroupe, ClientGroupeCriteria, ClientGroupeSegment } from '../../../models/clientGroupe';
import { SortDescriptor } from '@progress/kendo-data-query';
import { WinClientVille } from '../../../models/ville';
import { WinClientLocalite } from '../../../models/localite';
import { WinclientLocaliteService } from '../../../Services/winclient.localite.service';
import { TranscoClientService } from '../../../Services/winclient.transcoClient.service';
import { ClientSiteService } from '../../../Services/winclient.clientSite.service';
import { ClientSite, ClientSiteFilter } from '../../../models/clientSite';
import { TranscoClient, TranscoClientCriteria } from '../../../models/transcoClient';
import { Observable, OperatorFunction, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, switchMap } from 'rxjs/operators';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-list-anomalies-client-groupe',
  templateUrl: './list-anomalies-client-groupe.component.html',
  styleUrls: ['./list-anomalies-client-groupe.component.scss']
})
export class ListAnomaliesClientGroupeComponent implements OnInit {

  anomalies: GridDataResult = {
    data: [],
    total: 0
  };

  navigation: Pagination = {
    skip: 0,
    pageSize: 25,
    sortField: '',
    sortMethod: '',
  };

  selectedStatut: string = 'TOUS';
  anomalieCriteria: Partial<AnomalieCriteria> = new AnomalieCriteria({
    statut: "ouverte"
  });
  anomalieFilterForm: FormGroup;
  isLoadingClientGroupeDoublon: boolean = false;
  clientGroupeDoublon: ClientGroupe
  AnomalieString = AnomalieStringEnum;
  AnomalieSort :SortDescriptor[] =[];

  // Drawer state management
  isDrawerFilterOpen: boolean = false;
  isConsultDrawerOpen: boolean = false;
  isAssocitionDrawerOpen: boolean = false;
  selectedClientGroupe: ClientGroupe = null;
  selectedAnomalie: Anomalie = null;
  clientDoublentConsulted: boolean = false;
  listVilles :WinClientVille[] = [];

  // Properties for linked sites grid in anomaly section
  linkedSitesForAnomaly: any = { data: [], total: 0 };
  isLoadingLinkedSitesForAnomaly = false;
  linkedSitesForAnomalyNavigation: Pagination = {
    skip: 0,
    pageSize: 10
  };
  linkedSitesForAnomalySort: SortDescriptor[] = [];

  // Properties for inline editing
  isEditingRaisonSociale = false;
  isEditingNomPharmacien = false;
  editedRaisonSociale = '';
  editedNomPharmacien = '';
  isSavingEdit = false;

  // Properties for full client group editing
  @ViewChild('fullEditModal') fullEditModal: TemplateRef<any>;
  clientGroupeForm: FormGroup;
  formSubmitted = false;
  cities: WinClientVille[] = [];
  localities: WinClientLocalite[] = [];
  modalRef: NgbModalRef;
  segmentOptions = [
    { label: 'Pharmacie', value: ClientGroupeSegment.PHARMACIE },
    { label: 'Parapharmacie', value: ClientGroupeSegment.PARAPHARMACIE },
    { label: 'Centre Beauté', value: ClientGroupeSegment.CENTRE_BEAUTE }
  ];

  // Anomalie type options for dropdown
  anomalieTypeOptions = [
    { label: 'Tous', value: null },
    { label: 'Doublon exact', value: AnomalieType.DOUBLON_EXACT },
    { label: 'Doublon probable', value: AnomalieType.DOUBLON_PROBABLE },
    { label: 'Raison sociale contient une ville', value: AnomalieType.RAISON_VILLE },
    { label: 'Un mot de 1 caractère', value: AnomalieType.NOM_CAR_1 },
    { label: 'Nom trop court', value: AnomalieType.NOM_TROP_COURT },
    { label: 'Champ manquant', value: AnomalieType.CHAMP_MANQUANT }
  ];

  // Statut options for switch component
  statutOptions = [
    { label: 'Tous', value: null },
    { label: 'Ouverte', value: StatutAnomalie.OUVERTE },
    { label: 'Fermée', value: StatutAnomalie.FREMEE }
  ];

  constructor(
    private anomaliesService: WinclientAnomaliesService,
    private alertService: AlertService,
    private userInputService: UserInputService,
    private router: Router,
    private fb: FormBuilder,
    private clientGroupeService: ClientGroupeService,
    private clientSiteService: ClientSiteService,
    private winClientVilleService: WinclientVilleService,
    private winclientLocaliteService: WinclientLocaliteService,
    private transcoService: TranscoClientService,
    private modalService: NgbModal
  ) {
    this.initClientGroupeForm();
  }

  get hasActiveFilters(): boolean {
    return Object.keys(this.anomalieCriteria).some(key => this.anomalieCriteria[key] !== null);
  }

  ngOnInit() {
    this.initializeFilterForm();
    this.getAllAnomalies();
    this.loadVilles();
    this.loadLocalities();
  }


  loadVilles() {
    this.winClientVilleService.villes$.subscribe({
      next: (villes) => {
        this.listVilles = villes;
      }
    });
  }

  initializeFilterForm() {
    this.anomalieFilterForm = this.fb.group({
      codeClientGroupe: [null],
      anomalieType: [null],
      dateCreationAnomalie: [null],
      classification: [null],
      codeClientGroupeDoublon: [null],
      nomPharmacien: [null],
      pourcentageMatchingMax: [null],
      pourcentageMatchingMin: [null],
      raisonSociale: [null],
      villeLibelle: [null],
      dateTraitement: [null],
      statut: ["ouverte"],
      estUtilise: [null]
    });
  }

  getAllAnomalies() {
    this.anomaliesService.searchAnomalies(this.navigation,this.anomalieCriteria).subscribe(res => {
      this.anomalies.data = res.content;
      this.anomalies.total = res.totalElements;
    });
  }

  loadClientGroupeDoublon(anomalie: Anomalie) {
    this.isLoadingClientGroupeDoublon = true;
    console.log("Loading client groupe doublon for anomalie:", anomalie);
    const clientGroupeCriteria = new ClientGroupeCriteria({
      codeClientGroupe: String(anomalie.codeClientGroupeDoublon),
      page: 0,
      size: 1
    });
    this.clientGroupeService.searchClientGroupe({skip:0,pageSize:1},clientGroupeCriteria).subscribe({
      next: (res) => {
        this.clientGroupeDoublon = res.content.length > 0 ? res.content[0] : null;
      },
      complete: () => {
        this.isLoadingClientGroupeDoublon = false;
      }
    });
  }

  changerStatutAnomalie(anomalie: Anomalie) {
    this.clearClickedItem();
    anomalie['isClicked'] = true;
    
    this.userInputService.confirm("Confirmer le changement de statut ?",`Voulez-vous vraiment changer ${anomalie.statut === StatutAnomalie.FREMEE ? 'ouvrir' : 'fermer'} cette anomalie ?`,"Oui","Non").then(result => {
      if(result){
        const newStatus = anomalie.statut === StatutAnomalie.FREMEE ? StatutAnomalie.OUVERTE : StatutAnomalie.FREMEE;
        this.anomaliesService.changerStatutAnomalie(anomalie.id, newStatus).subscribe(res => {
          this.getAllAnomalies();
        });
      }
    }).catch(() => { });
  }

  changeStatutFilter(filterValue: string) {
    this.selectedStatut = filterValue;
    this.anomalieCriteria = new AnomalieCriteria({
      statut: filterValue === 'TOUS' ? null : filterValue === 'OUVERT' ? StatutAnomalie.OUVERTE : StatutAnomalie.FREMEE
    });
    this.navigation.skip = 0;
    this.getAllAnomalies();
  }

  lancerBatch() {
    this.anomaliesService.lancerBatch().subscribe(res => {
      this.alertService.success("Le batch a été lancé avec succès");
      this.getAllAnomalies();
    });
  }

  onCellClick(event: CellClickEvent) {
    if( event.column.field !== 'codeClientGroupe' && event.column.field !== 'codeClientGroupeDoublon'  && event.column.field){
      const dataItem = event.dataItem as Anomalie;
      this.consultAnomalie(dataItem);
    }
  }

  buildSortField(){
    return this.AnomalieSort && this.AnomalieSort.length > 0 && this.AnomalieSort[0].dir ? `${this.AnomalieSort[0].field},${this.AnomalieSort[0].dir}` : null;
  }

  onSortChange(sort: SortDescriptor[]) {
  this.AnomalieSort = sort;
    if(this.AnomalieSort && this.AnomalieSort.length > 0 && this.AnomalieSort[0].dir){
      this.navigation.sortField = sort[0].field;
      this.navigation.sortMethod = sort[0].dir;
      this.getAllAnomalies();
    }else{
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
      this.getAllAnomalies();
    }
  }


  pageChange(skip: number) {
    this.navigation.skip = skip;
    this.getAllAnomalies();
  }

  rowClass(args: RowClassArgs) {
    if (args.dataItem?.isClicked) {
      return { 'highlight-row-clicked': true };
    }
    return '';
  }

  clearClickedItem() {
    this.anomalies.data.forEach(item => {
      delete item['isClicked'];
    });
  }

  // Drawer management methods
  toggleFilterDrawer(open: boolean) {
    this.isDrawerFilterOpen = open;
  }

  toggleConsultDrawer(open: boolean) {
    this.isConsultDrawerOpen = open;
    if (!open) {
      this.selectedClientGroupe = null;
      this.selectedAnomalie = null;
    }
  }

  // Method to handle client groupe consultation
  consultClientGroupe(dataItem: Anomalie,isDoublon: boolean = false) {
    this.clientDoublentConsulted = false;
    this.clearClickedItem();
    dataItem['isClicked'] = true;
    this.selectedClientGroupe = dataItem.clientGroupe;
    this.isAssocitionDrawerOpen = true; 
    if (dataItem.codeClientGroupeDoublon && isDoublon) {
      this.clientDoublentConsulted = true;
      this.loadClientGroupeDoublon(dataItem);
     }
  }

  consultAnomalie(dataItem: Anomalie) {
    this.clientDoublentConsulted = false;
     this.clearClickedItem();
    dataItem['isClicked'] = true;
    this.selectedClientGroupe = dataItem.clientGroupe;
    this.selectedAnomalie = dataItem;
    this.toggleConsultDrawer(true);
    if (dataItem.codeClientGroupeDoublon && [AnomalieStringEnum.DOUBLON_EXACT, AnomalieStringEnum.DOUBLON_PROBABLE].includes(dataItem.anomalie)) {
      this.loadClientGroupeDoublon(dataItem);
     }

     // Load linked sites for NOM_TROP_COURT anomaly
     if (dataItem.anomalie === AnomalieStringEnum.NOM_TROP_COURT) {
       this.loadLinkedSitesForAnomaly();
     }
  }

  // Add to your component class
    highlightOneCharWord(text: string): string {
      if (!text) return '';
      
      // Match words that are a single character
      return text.replace(/\b(\w{1})\b/g, '<span class="text-danger fw-bold">$1</span>');
    }

    highlightCityInName(name: string, specificCity?: string): string {
      // if (!name) return '';
      
      // // First check the specific city if provided
      // if (specificCity) {
      //   // Use word boundaries to ensure we match complete words
      //   const regex = new RegExp(`\\b(${this.escapeRegExp(specificCity)})\\b`, 'gi');
      //   if (regex.test(name)) {
      //     return name.replace(regex, '<span class="text-danger fw-bold">$1</span>');
      //   }
      // }
      
      // Then check against all cities in the list
      if (this.listVilles && this.listVilles.length > 0) {
        let result = name;
        // Sort cities by length in descending order to match longer names first
        const sortedCities = [...this.listVilles].sort((a, b) => 
          (b.libelle?.length || 0) - (a.libelle?.length || 0));
        
        for (const city of sortedCities) {
          if (city.libelle && city.libelle.trim()) {
            // Use word boundaries for better matching
            const regex = new RegExp(`\\b(${this.escapeRegExp(city.libelle)})\\b`, 'gi');
            if (regex.test(result)) {
              result = result.replace(regex, '<span class="text-danger fw-bold">$1</span>');
            }else{
              console.warn(`City "${city.libelle}" not found in name "${name}"`);
            }
          }
        }
        return result;
      }else{
        console.warn("No cities available for highlighting");
      }
      
      return name + "op";
    }

    // Helper function to escape special regex characters
    private escapeRegExp(string: string): string {
      return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    // Add to your component class
    hasOneCharWord(text: string): boolean {
      if (!text) return false;
      // Check if text contains any single character word
      return /\b\w{1}\b/.test(text);
    }

    isNameTooShort(text: string): boolean {
      if (!text) return false;
      // Consider a name too short if it's less than 7 characters
      // You can adjust this threshold based on your requirements
      return text.trim().length < 7;
    }

    // Add to your component class
  highlightDifferences(text1: string, text2: string): string {
    if (!text1) return '<span class="text-danger fw-bold">Manquant</span>';
    if (!text2) return text1;
    
    if (text1 === text2) return text1;
    
    // Check if words are simply in a different order (like first/last name swap)
    const words1 = text1.split(/\s+/);
    const words2 = text2.split(/\s+/);
    
    // If both texts have the same words but in different order
    if (words1.length > 1 && words1.length === words2.length && 
        words1.every(w => words2.some(w2 => w2 === w || this.areWordsSimilar(w, w2)))) {
      
      let result = '';
      for (let word of words1) {
        // If exact word exists in text2, it's just reordered
        if (words2.includes(word)) {
          result += `<span class="text-warning fw-bold">${word}</span> `;
        } 
        // Word is similar but not exact (may have small typos)
        else {
          const similarWord = words2.find(w2 => this.areWordsSimilar(word, w2));
          if (similarWord) {
            result += `<span class="text-danger fw-bold">${word}</span> `;
          } else {
            result += `<span class="text-danger fw-bold">${word}</span> `;
          }
        }
      }
      return result.trim();
    }
    
    // Regular character-by-character highlighting for other cases
    let result = '';
    let i = 0;
    let inDiffBlock = false;
    
    while (i < text1.length) {
      if (i >= text2.length || text1[i] !== text2[i]) {
        if (!inDiffBlock) {
          result += '<span class="text-danger fw-bold">';
          inDiffBlock = true;
        }
      } else {
        if (inDiffBlock) {
          result += '</span>';
          inDiffBlock = false;
        }
      }
      
      result += text1[i];
      i++;
    }
    
    if (inDiffBlock) {
      result += '</span>';
    }
    
    return result;
  }

  /**
   * Checks if two words are similar (handles typos, missing letters)
   */
  private areWordsSimilar(word1: string, word2: string): boolean {
    // If length difference is too large, they're not similar
    if (Math.abs(word1.length - word2.length) > 2) return false;
    
    // Count differences (Levenshtein distance simplified)
    let differences = 0;
    const maxLen = Math.max(word1.length, word2.length);
    
    for (let i = 0; i < maxLen; i++) {
      // If positions exist in both words and are different
      if (i < word1.length && i < word2.length && word1[i].toLowerCase() !== word2[i].toLowerCase()) {
        differences++;
      } 
      // If position exists in only one word
      else if ((i >= word1.length && i < word2.length) || (i < word1.length && i >= word2.length)) {
        differences++;
      }
      
      // Too many differences
      if (differences > 2) return false;
    }
    
    return true;
  }

  // Filter form submission
  onFilterSubmit() {
    const formValue = this.anomalieFilterForm.value;
    this.anomalieCriteria = new AnomalieCriteria({
      codeClientGroupe: formValue.codeClientGroupe || null,
      anomalieType: formValue.anomalieType || null,
      dateCreationAnomalie: formValue.dateCreationAnomalie || null,
      classification: formValue.classification || null,
      codeClientGroupeDoublon: formValue.codeClientGroupeDoublon || null,
      nomPharmacien: formValue.nomPharmacien || null,
      pourcentageMatchingMax: formValue.pourcentageMatchingMax || null,
      pourcentageMatchingMin: formValue.pourcentageMatchingMin || null,
      raisonSociale: formValue.raisonSociale || null,
      villeLibelle: formValue.villeLibelle || null,
      dateTraitement: formValue.dateTraitement || null,
      statut: formValue.statut || null,
      estUtilise: formValue.estUtilise || null
    });
    this.anomalieCriteria = this.cleanFilterQueryParams(this.anomalieCriteria);
    this.navigation.skip = 0;
    this.getAllAnomalies();
    this.toggleFilterDrawer(false);
  }

  cleanFilterQueryParams(filters: Partial<AnomalieCriteria>) {
    // remove undifined values and empty strings
    Object.keys(filters).forEach(key => {
      if (filters[key] === undefined || filters[key] === '' || filters[key] === null || (typeof filters[key] === 'string' && filters[key].trim() === '')) {
        delete filters[key];
      }
    });
    return filters;
  }

  // Filter command handler (reset)
  filterCommand(command: string) {
    if (command === 'reset') {
      this.anomalieFilterForm.reset();
      this.anomalieCriteria = new AnomalieCriteria({});
      this.navigation.skip = 0;
      this.getAllAnomalies();
      this.isDrawerFilterOpen = false;
    }
    if (command === 'refresh') {
      this.navigation.skip = 0;
      this.getAllAnomalies();
      this.isDrawerFilterOpen = false;
    }

  }


  declareGoodPrimaire(anomalieDoublon: Anomalie,) {
    anomalieDoublon['indexOfGood'] = 1;
    this.alertService.success("Déclaration du bon primaire effectuée avec succès.");
    
  }

  declareGoodSecondaire(anomalieDoublon: Anomalie) {
    anomalieDoublon['indexOfGood'] = 2;
    this.alertService.success("Déclaration du bon secondaire effectuée avec succès.");
  }




 async processCorrectiveAction() {
    const anomalie = this.selectedAnomalie;
    const goodClientGroupeCode = anomalie?.indexOfGood === 1 ? anomalie.codeClientGroupe : anomalie.codeClientGroupeDoublon;
    const badClientGroupeCode = anomalie?.indexOfGood === 1 ? anomalie.codeClientGroupeDoublon : anomalie.codeClientGroupe;

    const listClientSiteLiees = await this.loadClientSites(badClientGroupeCode);


    
    for (const clientSite of listClientSiteLiees) {
      const transcoClientCriteria = new TranscoClientCriteria({
        codeClientSite: clientSite.cliCode,
        codeSite: clientSite.codeSite,  
      });
      
      try {
        const transcoClients = await new Promise<TranscoClient[]>((resolve, reject) => {
          this.transcoService.searchTranscoClient(transcoClientCriteria).subscribe({
            next: (value) => resolve(value),
            error: (err) => reject(err)
          });
        });
        
        if (transcoClients && transcoClients.length > 0) {
          const transcoClient = transcoClients[0];
          await new Promise((resolve, reject) => {
            this.transcoService.deleteTranscoClient(transcoClient.id).subscribe({
              next: (res) => resolve(res),
              error: (err) => reject(err)
            });
          });
        }
      } catch (error) {
        console.error('Error processing transco client:', error);
      }
    }

    await this.excludeClientGroupe(badClientGroupeCode);

    listClientSiteLiees.forEach(async clientSite => {
      await this.CreateTranscoClient(clientSite, goodClientGroupeCode);
    });
    this.alertService.success("Action corrective effectuée avec succès.");
    this.selectedAnomalie.indexOfGood = undefined;
  }


  async CreateTranscoClient(clientSite: ClientSite,newCodeGroupe:string) {
    const transcoClient = new TranscoClient({
        backupCodeClientGroupe: newCodeGroupe,
        codeClientGroupe :  newCodeGroupe,
        codeClientSite:clientSite.cliCode,
        codeSite:clientSite.codeSite,
    })
    console.log("Transco Requested to Create :",transcoClient)
    return new Promise((resolve, reject) => {
      this.transcoService.createTranscoClient(transcoClient).subscribe({
        next: (res) => {
          resolve(res);
        },
        error: (err) => {
          reject(err);
        }
      });
    }); 
  }

   async excludeClientGroupe(codeClientGroupe:string){
   return new Promise((resolve, reject) => {
    this.clientGroupeService.excludeClientGroupe(codeClientGroupe).subscribe(res => {
      resolve(res);
    },(err) => {      
      reject(err);
    });
   })
  }



  async loadClientSites(codeGroupe: string) : Promise<ClientSite[]> {
     const clientSiteFilter =  new ClientSiteFilter({codeGroupe});
     
     return new Promise((resolve, reject) => {
      this.clientSiteService.searchClientSite(clientSiteFilter, {page: 0, size: 1000}).subscribe({
        next: (res) => {
          resolve(res.content);
        },
        error: (err) => {
          console.error("Error loading client sites:", err);
          reject(err);
        }
      });

    });
  }

  // Load linked sites for anomaly section
  loadLinkedSitesForAnomaly(page = 0) {
    if (!this.selectedClientGroupe?.codeClientGroupe) return;

    this.linkedSitesForAnomalyNavigation.skip = page * this.linkedSitesForAnomalyNavigation.pageSize;

    const clientSiteFilter = new ClientSiteFilter({
      codeGroupe: String(this.selectedClientGroupe.codeClientGroupe),
    });

    const paginationParams = {
      page,
      size: this.linkedSitesForAnomalyNavigation.pageSize,
      sort: this.linkedSitesForAnomalySort[0] ? `${this.linkedSitesForAnomalySort[0]?.field},${this.linkedSitesForAnomalySort[0]?.dir || 'asc'}` : '',
    };

    this.isLoadingLinkedSitesForAnomaly = true;
    this.clientSiteService.searchClientSite(clientSiteFilter, paginationParams).subscribe({
      next: (res) => {
        this.linkedSitesForAnomaly.data = res.content;
        this.linkedSitesForAnomaly.total = res.totalElements;
      },
      complete: () => {
        this.isLoadingLinkedSitesForAnomaly = false;
      }
    });
  }

  // Update client group field from selected site
  updateClientGroupeFromSite(fieldName: string, siteValue: string, siteName: string) {
    if (!this.selectedClientGroupe?.id) return;

    const updateData = {
      ...this.selectedClientGroupe,
      [fieldName]: siteValue
    };

    // Remove the id from the update data as it's passed separately
    const { id, ...updatePayload } = updateData;

    this.clientGroupeService.updateClientGroupe(this.selectedClientGroupe.id, updatePayload).subscribe({
      next: (updatedClientGroupe) => {
        this.selectedClientGroupe = updatedClientGroupe;
        this.alertService.success(`${fieldName === 'nomPharmacien' ? 'Nom Pharmacien' : 'Raison Sociale'} mis à jour avec la valeur "${siteValue}" du site "${siteName}"`);

        // Update the anomaly data in the grid
        const anomalyIndex = this.anomalies.data.findIndex(a => a.clientGroupe.id === updatedClientGroupe.id);
        if (anomalyIndex !== -1) {
          this.anomalies.data[anomalyIndex].clientGroupe = updatedClientGroupe;
        }

        // Check if the anomaly is now resolved
        this.checkAnomalyResolution();
      },
      error: (error) => {
        this.alertService.error('Erreur lors de la mise à jour du client groupe');
      }
    });
  }

  // Check if the current anomaly is resolved after updating fields
  checkAnomalyResolution() {
    if (!this.selectedClientGroupe || !this.selectedAnomalie) return;

    const isRaisonSocialeValid = this.selectedClientGroupe.raisonSociale &&
                                this.selectedClientGroupe.raisonSociale.trim().length > 3;
    const isNomPharmacienValid = this.selectedClientGroupe.nomPharmacien &&
                                this.selectedClientGroupe.nomPharmacien.trim().length > 3;

    if (isRaisonSocialeValid && isNomPharmacienValid) {
      this.alertService.info('✅ L\'anomalie "Nom trop court" semble être résolue. Les champs ont maintenant une longueur suffisante.');

      // Optionally, you could automatically close the anomaly here
      // this.changerStatutAnomalie(this.selectedAnomalie);
    }
  }

  // Pagination and sorting for linked sites in anomaly
  linkedSitesForAnomalyPageChange(skip: number): void {
    this.linkedSitesForAnomalyNavigation.skip = skip;
    const page = Math.ceil(skip / this.linkedSitesForAnomalyNavigation.pageSize);
    this.loadLinkedSitesForAnomaly(page);
  }

  linkedSitesForAnomalySortChange(sort: SortDescriptor[]): void {
    this.linkedSitesForAnomalySort = sort;
    this.linkedSitesForAnomalyNavigation.skip = 0;
    this.loadLinkedSitesForAnomaly();
  }

  // Inline editing methods
  startEditingRaisonSociale() {
    this.isEditingRaisonSociale = true;
    this.editedRaisonSociale = this.selectedClientGroupe?.raisonSociale || '';

    // Auto-focus the input field after view update
    setTimeout(() => {
      const input = document.querySelector('#raisonSocialeInput') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    }, 100);
  }

  startEditingNomPharmacien() {
    this.isEditingNomPharmacien = true;
    this.editedNomPharmacien = this.selectedClientGroupe?.nomPharmacien || '';

    // Auto-focus the input field after view update
    setTimeout(() => {
      const input = document.querySelector('#nomPharmacienInput') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    }, 100);
  }

  cancelEditingRaisonSociale() {
    this.isEditingRaisonSociale = false;
    this.editedRaisonSociale = '';
  }

  cancelEditingNomPharmacien() {
    this.isEditingNomPharmacien = false;
    this.editedNomPharmacien = '';
  }

  saveRaisonSociale() {
    if (!this.editedRaisonSociale.trim()) {
      this.alertService.error('La raison sociale ne peut pas être vide');
      return;
    }

    if (this.editedRaisonSociale.trim().length <= 3) {
      this.alertService.warning('La raison sociale est encore trop courte (≤3 caractères)');
    }

    this.saveFieldEdit('raisonSociale', this.editedRaisonSociale.trim(), () => {
      this.isEditingRaisonSociale = false;
      this.editedRaisonSociale = '';
    });
  }

  saveNomPharmacien() {
    if (!this.editedNomPharmacien.trim()) {
      this.alertService.error('Le nom du pharmacien ne peut pas être vide');
      return;
    }

    if (this.editedNomPharmacien.trim().length <= 3) {
      this.alertService.warning('Le nom du pharmacien est encore trop court (≤3 caractères)');
    }

    this.saveFieldEdit('nomPharmacien', this.editedNomPharmacien.trim(), () => {
      this.isEditingNomPharmacien = false;
      this.editedNomPharmacien = '';
    });
  }

  private saveFieldEdit(fieldName: string, newValue: string, onSuccess: () => void) {
    if (!this.selectedClientGroupe?.id) return;

    this.isSavingEdit = true;

    const updateData = {
      ...this.selectedClientGroupe,
      [fieldName]: newValue
    };

    const { id, ...updatePayload } = updateData;

    this.clientGroupeService.updateClientGroupe(this.selectedClientGroupe.id, updatePayload).subscribe({
      next: (updatedClientGroupe) => {
        this.selectedClientGroupe = updatedClientGroupe;
        this.alertService.success(`${fieldName === 'nomPharmacien' ? 'Nom Pharmacien' : 'Raison Sociale'} mis à jour avec succès`);

        // Update the anomaly data in the grid
        const anomalyIndex = this.anomalies.data.findIndex(a => a.clientGroupe.id === updatedClientGroupe.id);
        if (anomalyIndex !== -1) {
          this.anomalies.data[anomalyIndex].clientGroupe = updatedClientGroupe;
        }

        // Check if the anomaly is now resolved
        this.checkAnomalyResolution();

        onSuccess();
      },
      error: (error) => {
        this.alertService.error('Erreur lors de la mise à jour du client groupe');
      },
      complete: () => {
        this.isSavingEdit = false;
      }
    });
  }

  // Handle Enter key press to save
  onEnterKey(fieldType: 'raisonSociale' | 'nomPharmacien') {
    if (fieldType === 'raisonSociale') {
      this.saveRaisonSociale();
    } else {
      this.saveNomPharmacien();
    }
  }

  // Handle Escape key press to cancel
  onEscapeKey(fieldType: 'raisonSociale' | 'nomPharmacien') {
    if (fieldType === 'raisonSociale') {
      this.cancelEditingRaisonSociale();
    } else {
      this.cancelEditingNomPharmacien();
    }
  }

  // Initialize client group form
  initClientGroupeForm() {
    this.clientGroupeForm = this.fb.group({
      id: [null],
      codeClientGroupe: [null],
      raisonSociale: ['', Validators.required],
      nomPharmacien: ['', Validators.required],
      adresse1: ['', Validators.required],
      adresse2: [''],
      localite: [''],
      ville: [null, Validators.required],
      telephone: ['', [Validators.required]],
      telephone2: [null],
      gsm: [null],
      whatsapp: [null],
      ice: [null],
      email: [null, [Validators.email]],
      patente: [''],
      inpe: [null],
      longitude: [null],
      latitude: [null],
      classification: [''],
      tag: [''],
      segment: [this.segmentOptions[0].value]
    });
  }

  // Load cities data
  loadCities() {
    this.winClientVilleService.getAllVilles().subscribe({
      next: (response) => {
        this.cities = response.content || [];
      },
      error: (error) => {
        console.error('Error loading cities:', error);
      }
    });
  }

  // Load localities data
  loadLocalities() {
    this.winclientLocaliteService.getAllLocalites().subscribe({
      next: (localities) => {
        this.localities = localities || [];
      },
      error: (error) => {
        console.error('Error loading localities:', error);
      }
    });
  }

  // Typeahead search for localities
  searchLocaliteTypeahead: OperatorFunction<string, readonly WinClientLocalite[]> = (
    text$: Observable<string>
  ) =>
    text$.pipe(
      debounceTime(200),
      distinctUntilChanged(),
      filter((term) => term.length >= 2),
      switchMap((term) => {
        const targetLocalite = this.localities.filter((v) =>
          v.libLocalite.toLowerCase().indexOf(term.toLowerCase()) > -1
        );
        return of(targetLocalite);
      })
    );

  // Formatter for locality typeahead
  formatterLocalite = (localite: WinClientLocalite) => localite.libLocalite;

  // Formatter for city typeahead
  formatterVille = (ville: WinClientVille) => ville.libelle;

  // Start editing full client group
  startEditingFullClientGroupe() {
    if (this.selectedAnomalie?.clientGroupe) {
      this.formSubmitted = false;
      this.patchFormValues(this.selectedAnomalie.clientGroupe);
      this.modalRef = this.modalService.open(this.fullEditModal, {
        size: 'xl',
        backdrop: 'static',
        keyboard: false
      });
    }
  }

  // Patch form values from client group
  private patchFormValues(clientGroupe: ClientGroupe) {
    const localite = this.localities.find(l => l.libLocalite === clientGroupe.localite);
    this.clientGroupeForm.patchValue({
      id: clientGroupe.id,
      codeClientGroupe: clientGroupe.codeClientGroupe,
      raisonSociale: clientGroupe.raisonSociale,
      nomPharmacien: clientGroupe.nomPharmacien,
      adresse1: clientGroupe.adresse1,
      adresse2: clientGroupe.adresse2,
      localite,
      ville: clientGroupe.ville,
      telephone: clientGroupe.telephone,
      telephone2: clientGroupe.telephone2,
      gsm: clientGroupe.gsm,
      whatsapp: clientGroupe.whatsapp,
      email: clientGroupe.email,
      ice: clientGroupe.ice,
      patente: clientGroupe.patente,
      inpe: clientGroupe.inpe,
      longitude: clientGroupe.longitude,
      latitude: clientGroupe.latitude,
      classification: clientGroupe.classification,
      tag: clientGroupe.tag,
      segment: clientGroupe.segment
    });
  }

  // Convert form to client group object
  formToClientGroupe(): ClientGroupe {
    const formValue = this.clientGroupeForm.value;
    return new ClientGroupe({
      id: formValue.id,
      codeClientGroupe: formValue.codeClientGroupe,
      raisonSociale: formValue.raisonSociale,
      nomPharmacien: formValue.nomPharmacien,
      adresse1: formValue.adresse1,
      adresse2: formValue.adresse2,
      localite: formValue.localite?.libLocalite || (typeof formValue.localite === 'string' ? formValue.localite : null),
      ville: formValue.ville,
      telephone: formValue.telephone,
      telephone2: formValue.telephone2,
      gsm: formValue.gsm,
      whatsapp: formValue.whatsapp,
      email: formValue.email,
      ice: formValue.ice,
      patente: formValue.patente,
      inpe: formValue.inpe,
      longitude: formValue.longitude,
      latitude: formValue.latitude,
      classification: formValue.classification,
      tag: formValue.tag,
      segment: formValue.segment
    });
  }

  // Save full client group
  saveFullClientGroupe() {
    this.formSubmitted = true;

    if (!this.clientGroupeForm.valid) {
      this.alertService.error("Formulaire non valide");
      return;
    }

    const clientGroupe = this.formToClientGroupe();
    const { id, ...clientGroupePayload } = clientGroupe;

    if (!id) {
      this.alertService.error("ID du client groupe manquant");
      return;
    }

    this.isSavingEdit = true;

    this.clientGroupeService.updateClientGroupe(id, clientGroupePayload).subscribe({
      next: (updatedClientGroupe) => {
        // Update the selected anomaly with the new client group data
        if (this.selectedAnomalie) {
          this.selectedAnomalie.clientGroupe = updatedClientGroupe;
        }

        this.formSubmitted = false;
        this.modalRef?.close();
        this.alertService.success(`Le Client Groupe ${updatedClientGroupe.raisonSociale} (${updatedClientGroupe.nomPharmacien}) a été modifié avec succès`);

        // Check if anomaly is resolved after the update
        this.checkAnomalyResolution();
      },
      error: (error) => {
        console.error('Error updating client group:', error);
        this.alertService.error(error.error?.message || 'Erreur lors de la mise à jour du client groupe');
      },
      complete: () => {
        this.isSavingEdit = false;
      }
    });
  }
}
