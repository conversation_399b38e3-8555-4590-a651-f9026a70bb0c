<!-- Topbar Start -->
<div class="navbar-custom px-0" [class]="cssClasses">
    <!-- <div class="overflow-hidden position-absolute" style="height: 55px; width: 100%;">
        <img alt="header-img" src="assets/images/quebg.svg" style="
        position: absolute;
        opacity: .15;
        width: 600px;
        right: -155px;
        top: -50px;
        transform: scaleY(-1); z-index: -1;
      "><img alt="header-img" src="assets/images/quebg.svg" style="
        position: absolute;
        opacity: .15;
        width: 600px;
        left: -150px; 
        top: -50px;
        transform: scale(-1); z-index: -1;
      ">
    </div> -->
    <div class=" px-2 ">
        <!-- LOGO -->
        <a routerLink="/" class="topnav-logo" *ngIf="!hideLogo && topbarDark" tabindex="-1">
            <span class="topnav-logo-lg">
                <img src="assets/images/logo-light.png" alt="" height="16">
            </span>
            <span class="topnav-logo-sm">
                <img src="assets/images/logo_sm.png" alt="" height="16">
            </span>
        </a>

        <!-- LOGO -->
        <a routerLink="/" class="topnav-logo" *ngIf="!hideLogo && !topbarDark" tabindex="-1">
            <span class="topnav-logo-lg">
                <img src="assets/images/logo-dark.png" alt="" height="16">
            </span>
            <span class="topnav-logo-sm">
                <img src="assets/images/logo_sm_dark.png" alt="" height="16">
            </span>
        </a>
      

        <ul class="list-unstyled topbar-menu float-end mb-0">
            <!-- <li class="d-flex justify-content-center align-items-center mt-md-2 mt-1 me-1" *ngIf="isLabo">
                <div class="rounded-pill d-flex justify-content-center align-items-center px-1  py-1 px-md-2 fs-6 " style=" background: radial-gradient(circle, rgba(246,246,246,1) 0%, rgb(179, 174, 164) 100%);" >
               
                   <span  style="color: #94908a;">Freemium</span>

                </div>
             
            </li> -->
            <!-- <li class="dropdown notification-list d-lg-none" ngbDropdown>
                <a ngbDropdownToggle class="nav-link dropdown-toggle arrow-none" id="mobileDropdown"
                    href="javascript: void(0);" aria-expanded="false">
                    <i class="dripicons-search noti-icon"></i>
                </a>
                <div ngbDropdownMenu aria-labelledby="mobileDropdown" class="dropdown-menu-animated dropdown-lg p-0">
                    <form ngbDropdownItem class="p-3">
                        <input type="text" class="form-control" placeholder="Search ..."
                            aria-label="Recipient's username">
                    </form>
                </div>
            </li> -->

            <!-- <li class="dropdown notification-list topbar-dropdown"
                [class]="layoutType!=='vertical' ? 'd-none d-lg-block' : ''" ngbDropdown>
                <a ngbDropdownToggle class="nav-link arrow-none" id="languageDropdown" href="javascript: void(0);"
                    aria-expanded="false">
                    <img [src]="selectedLanguage?.flag" [alt]="selectedLanguage?.name" class="me-0 me-sm-1" height="12">
                    <span class="align-middle d-none d-sm-inline-block">{{ selectedLanguage?.name }}</span>
                    <i class="mdi mdi-chevron-down d-none d-sm-inline-block align-middle"></i>
                </a>
                <div ngbDropdownMenu aria-labelledby="languageDropdown"
                    class="dropdown-menu-animated topbar-dropdown-menu dropdown-menu-end">
                    <a ngbDropdownItem *ngFor="let language of languages;" href="javascript:void(0);"
                        class="notify-item" (click)="changeLanguage(language)">
                        <img [src]="language.flag" [alt]="language.name" class="me-1" height="12">
                        <span class="align-middle">{{language.name }}</span>
                    </a>
                </div>
            </li> -->

            <!-- <li class="dropdown notification-list" ngbDropdown>
                <a ngbDropdownToggle class="nav-link arrow-none" id="notificationDropdown" href="javascript: void(0);"
                    aria-expanded="false">
                    <i class="dripicons-bell noti-icon"></i>
                    <span class="noti-icon-badge"></span>
                </a>
                <div ngbDropdownMenu aria-labelledby="notificationDropdown"
                    class="dropdown-menu-end dropdown-menu-animated dropdown-lg">
                    <div ngbDropdownItem class="noti-title">
                        <h5 class="m-0">
                            <span class="float-end">
                                <a href="javascript: void(0);" class="text-dark">
                                    <small>Clear All</small>
                                </a>
                            </span>Notification
                        </h5>
                    </div>

                    <ngx-simplebar style="max-height: 210px;">
                        <div id="notification-items">
                            <a [routerLink]="item.redirectTo" class="notify-item" *ngFor="let item of notificationList"
                                ngbDropdownItem>
                                <div class="notify-icon" *ngIf="item.avatar">
                                    <img [src]="item.avatar" class="img-fluid rounded-circle" alt="">
                                </div>
                                <div class="notify-icon bg-{{item.bgColor}}" *ngIf="item.icon">
                                    <i [class]="item.icon"></i>
                                </div>
                                <p class="notify-details">{{item.text}}</p>
                                <p class="text-muted mb-0 user-msg">
                                    <small>{{item.subText}}</small>
                                </p>
                            </a>
                        </div>
                    </ngx-simplebar>
                    <a ngbDropdownItem href="javascript:void(0);"
                        class="text-center text-primary notify-item notify-all">
                        View All
                    </a>
                </div>
            </li> -->
            <!-- <li class="dropdown notification-list d-none d-sm-inline-block" ngbDropdown>
                <a ngbDropdownToggle class="nav-link arrow-none" id="brandDropdown" href="javascript:void(0)"
                    aria-expanded="false">
                    <i class="dripicons-view-apps noti-icon"></i>
                </a>
                <div ngbDropdownMenu aria-labelledby="brandDropdown"
                    class="dropdown-menu-end dropdown-menu-animated dropdown-lg p-0">
                    <div class="p-2">
                        <div class="row g-0 row-cols-3">
                            <div *ngFor="let app of apps" class="col">
                                <a ngbDropdownItem class="dropdown-icon-item" href="javascript:void(0);">
                                    <img [src]="app.logo" alt="app">
                                    <span>{{app.name}}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </li> -->
            <!-- <li class="notification-list">
                <a class="nav-link end-bar-toggle" href="javascript:void(0);" (click)="toggleRightSidebar()">
                    <i class="dripicons-gear noti-icon"></i>
                </a>
            </li> -->

            <li class="d-flex justify-content-center align-items-center mt-2">
                <div class="rounded-pill d-flex justify-content-center align-items-center "
                    style=" width:40px; height: 30px; "
                    [ngClass]="{ 'bg-danger':!network.isOnline}">
                    <!-- <span>{{network.isOnline ? '' : 'non '}}Connecté</span> -->
                    <i class="mdi" [ngClass]="{
                        ' mdi-access-point-network' : network.isOnline ,
                        ' mdi-signal-off' : !network.isOnline 
                    }"></i>

                    <!-- mdi-signal-off -->
                </div>
             
                <!-- <span class="p-1 bg-success rounded-pill">offline</span> -->
            </li>

            <li class="dropdown notification-list" ngbDropdown>

                <a ngbDropdownToggle class="nav-link nav-user arrow-none me-0" id="profileMenuDropdown" tabindex="-1"
                    href="javascript:void(0)" aria-expanded="false">
                    <span class="account-user-avatar">
                        <!-- <img [src]="'loggedInUser.avatar'" class="rounded-circle"> -->
                        <img src="assets/images/webfix-minimal.png" class="rounded-circle">

                    </span>
                    <span >
                        <!-- <span class="account-user-name">{{loggedInUser.name}}</span> -->
                        <span class="account-user-name">{{ loggedInTenant?.raisonSociale}}
                        </span>
                        <span class="account-position"> {{loggedInTenant?.prenom }} {{loggedInTenant?.nom}}

                        </span>
                        <!-- <span class="account-user-name text-capitalize">{{ loggedInUser?.username}}
                        </span> -->
                        <!-- {{loggedInUser?.mainAuthority | authorityName}} -->
                    </span>
                </a>
                <div ngbDropdownMenu aria-labelledby="profileMenuDropdown"
                    class="dropdown-menu-end dropdown-menu-animated topbar-dropdown-menu profile-dropdown me-2">
                    <!-- item -->
                    <div ngbDropdownItem class="dropdown-header noti-title">
                        <h6 class="text-overflow m-0">Bienvenue {{loggedInUser?.lastname}} </h6>
                    </div>
                    <!-- item -->
                    <a *ngFor="let option of profileOptions;" class="notify-item" ngbDropdownItem>
                        <i class="{{option.icon}} me-1"></i>
                        <span [routerLink]="[option.redirectTo]" style="cursor: pointer;">{{option.label}}</span>
                    </a>
                    <a *ngFor="let option of profileTenantOptions;" class="notify-item" ngbDropdownItem
                        [routerLink]="[option.redirectTo]">
                        <i class="{{option.icon}} me-1"></i>
                        <span style="cursor: pointer;">{{option.label}}</span>
                    </a>

                </div>
            </li>
        </ul>

        <!-- mobile menu toggle -->
        <button class="button-menu-mobile open-left d-none d-md-block" (click)="toggleSidebarWidth()" tabindex="-1"
            *ngIf="layoutType==='vertical'">
            <i class="mdi mdi-menu open-left-icon"></i>
        </button>
        <button class="button-menu-mobile open-left disable-btn" (click)="toggleMobileMenu($event)" tabindex="-1"
            *ngIf="layoutType==='vertical'">
            <i class="mdi mdi-menu open-left-icon"></i>
        </button>


        <a class="navbar-toggle open-left" [ngClass]="{'open': topnavCollapsed}" (click)="toggleMobileMenu($event)"
            tabindex="-1" *ngIf="layoutType==='horizontal'">
            <div class="lines">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </a>

        <a class="button-menu-mobile open-left disable-btn" (click)="toggleMobileMenu($event)" tabindex="-1"
            *ngIf="layoutType==='detached'">
            <div class="lines">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </a>

        <div class="app-search dropdown d-none d-lg-block" ngbDropdown container="body" placement="bottom-start">
            <!-- <form>
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Search..." id="topSearch" ngbDropdownToggle>
                    <span class="mdi mdi-magnify search-icon"></span>
                    <div class="input-group-append">
                        <button class="btn btn-primary" type="submit">Search</button>
                    </div>
                </div>
                <div ngbDropdownMenu aria-labelledby="topSearch" class="dropdown-menu-animated dropdown-lg mt-2"
                    id="search-dropdown">
                    
                    <div ngbDropdownItem class="dropdown-header noti-title">
                        <h5 class="text-overflow mb-2">Found <span class="text-danger">17</span> results</h5>
                    </div>
                    
                    <a ngbDropdownItem href="javascript:void(0);" class="notify-item"
                        *ngFor="let result of searchResults">
                        <i class="{{result.icon}} font-16 me-1"></i>
                        <span>{{result.text}}</span>
                    </a>
                    
                    <div ngbDropdownItem class="dropdown-header noti-title">
                        <h6 class="text-overflow mb-2 text-uppercase">Users</h6>
                    </div>
                    <div class="notification-list">
                        
                        <a ngbDropdownItem href="javascript:void(0);" class="notify-item"
                            *ngFor="let user of searchUsers">
                            <div class="d-flex">
                                <img class="d-flex me-2 rounded-circle" [src]="user.profile" [alt]="user.name"
                                    height="32">
                                <div class="w-100">
                                    <h5 class="m-0 font-14">{{user.name}}</h5>
                                    <span class="font-12 mb-0">{{user.position}}</span>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </form> -->
        </div>

<div class="col-auto px-0 d-flex justify-content-center align-items-center mt-1 me-sm-2" >
    <ul class="list-unstyled mb-0 text-white d-inline-block px-1 px-sm-4 mt-md-0 mt-2 " style="background-color: #435F2C !important; border-radius: 10px;">
      <li class="notification-list d-flex align-items-center justify-content-center">
        <div *ngIf="loggedInTenant" class="text-center fw-semibold text-uppercase fs-6 d-block d-sm-none" >
            <p class="mb-0">{{ loggedInTenant?.username }}</p>
        </div>
        <div *ngIf="loggedInTenant" class="text-center fw-semibold text-uppercase d-none d-md-block  fs-3" >
            <p class="mb-0">{{ loggedInTenant?.username }}</p>
        </div>
      </li>
    </ul>
  </div>
  
    

    </div>

</div>
<!-- Topbar End -->