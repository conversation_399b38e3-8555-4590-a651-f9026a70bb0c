import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetector<PERSON>ef, ViewChild, TemplateRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommandeService } from '../../../Services/achats/commande.service';
import { Subject } from 'rxjs';
import { PageChangeEvent, GridDataResult, RowClassArgs } from '@progress/kendo-angular-grid';

import { filter, retry, skip, takeUntil } from 'rxjs/operators';

import { Operateur } from '../../../models/common/operateur.model';
import { BaseRestorableComponent } from 'src/app/shared/base-restorable.component';
import { type } from 'os';
import { title } from 'process';

import { AuthService } from 'src/app/shared/services/auth.service';
import { Principal } from 'src/app/shared/models/principal';
import { log } from 'console';
import { AfterViewInit } from '@angular/core';
import moment from 'moment';
import { Pagination } from 'src/app/references-app/referential/models/pagination.interface';
import { ProduitSiteCriteriaForm } from '../../../models/produit/ref-produits/ProduitSiteCriteria.model';
import { UserInputService } from 'src/app/shared/services/user-input.service';
import { AlertService } from 'src/app/shared/services/alert.service';
import { ProduitService } from '../../../Services/produit/produit.service';
import { ExportPdfService } from 'src/app/shared/export/export-pdf.service';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { FournisseurService } from '../../../Services/tiers/fournisseur.service';
import { Noeud } from '../../../models/produit/ref-produits/noeud.model';
import { ProduitSite } from '../../../models/produit/ref-produits/produitSite.model';
import { CodeSites } from '../../produit-winplus/list-produit-winplus/data';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BootstrapColorClasses } from 'src/app/shared/confirm/confirm.component';
import { ProduitWinplus, ProduitWinplusDto } from '../../../models/produit/ref-produits/produitsWinplusDto.model';
import { ProduitWinplusCriteria } from '../../../models/produit/ref-produits/produitsWinplus.model';
import { TAPCriteria } from '../../../models/produit/ref-produits/tap.model';
import { SortDescriptor } from '@progress/kendo-data-query';
import { CustomSelectComponent } from 'src/app/shared/custom-select/custom-select.component';
@Component({
  selector: 'app-list-produits-sites',
  templateUrl: './list-produits-sites.component.html',
  styleUrls: ['./list-produits-sites.component.scss']
})
export class ListProduitsSitesComponent implements OnInit {

  siteProducts: GridDataResult = {
    data: [],
    total: 0
  };

   groupeProducts: GridDataResult = {
    data: [],
    total: 0
  };
  winplusProducts: GridDataResult = {
    data: [],
    total: 0
  };
  navigation: Pagination = {
    skip: 0,
    pageSize: 25,
    sortField: 'codeSite',
    sortMethod: 'asc',
  };
  groupeProductNavigation: Pagination = {
    skip: 0,
    pageSize: 25,
  };
  winplusProductNavigation: Pagination = {
    skip: 0,
    pageSize: 25
  };
  linkedProductSiteNavigation: Pagination = {
    skip: 0,
    pageSize: 25,
    sortMethod:'',
    sortField: '',
  };
  searchProduitGroupeModel = {
    designation: '',
    codeBarre: '',
    laboratoire: '',
  };

  searchProduitWinplusModel = {
    designation: '',
    codeBarre: '',
    laboratoire: '',
  };
  produitSiteCriteria: Partial<ProduitSiteCriteriaForm> = {};
  produitGroupeCriteria: Partial<ProduitSiteCriteriaForm> = {};
  produitWinplusCriteria: Partial<ProduitWinplusCriteria> = {};
  siteProductSort: SortDescriptor[] = [{field:'codeSite', dir: 'asc'}];
  selectedSites = []
  clickedItem: ProduitSite = null;
  showFilter: boolean = false;
  filterForm: FormGroup;
  winplusSearchMode: 'manual' | 'tap' = 'manual';
  sites = null;
  selectSites: any[] = [];
  isGroupePage: boolean = false;
  showTranscoDrawer: boolean = false;
  isTranscoGroupeLoading: boolean = false;
  isWinplusSearchLoading: boolean = false;
  isSearchGroupeLoading: boolean = false;
  isLoadingsearchLinkedProductSite: boolean = false;
  ProductGroupeTranscoded: ProduitSite[] = [];
  productSiteLinkedToGroupe: ProduitSite[] = [];
  ProductWinplusTranscoded: ProduitWinplusDto[] = [];
  isTranscoWinplusLoading: boolean = false;
  isFilterQueryParams: boolean = false;
  showSearchProduitGroupe: boolean = false;
  showSearchProduitWinplus: boolean = false;  isFetchedLinkedProductSite: boolean = false;
  productSiteLinkedToGroupeSort: SortDescriptor[] = [];
  @ViewChild(CustomSelectComponent) customSelect: CustomSelectComponent;
  productEditDesignation: string = '';
    // New properties for designation suggestions
  isLoadingDesignationSuggestions: boolean = false;
  designationSuggestions: ProduitWinplus[] = [];
  showWinplusDesignationSearch: boolean = false;
  isLoadingWinplusDesignationSearch: boolean = false;
  winplusDesignationSearchModel = {
    designation: ''
  };
  winplusDesignationSearchResults: ProduitWinplusDto[] = [];

  @ViewChild("updateProductDesigniation") editDesignationModal: NgbModalRef;

  constructor(
    private router: Router, private userInputService: UserInputService,
    private alertServ: AlertService, private productsService: ProduitService,
    private alertService: AlertService,private modalService : NgbModal,
    private fb: FormBuilder,private route: ActivatedRoute,

  ) {
    if (this.router.url.includes("produits-groupes")) {
      this.isGroupePage = true;
    }
  }



  closeTranscoDrawerAndReset() {
    this.showTranscoDrawer = false;
    this.clickedItem = null;
    this.ProductGroupeTranscoded = [];
    this.ProductWinplusTranscoded = [];
    this.groupeProducts.data = [];
    this.productSiteLinkedToGroupe = [];
    this.groupeProducts.total = 0;
    this.isFetchedLinkedProductSite = false;
    this.searchProduitGroupeModel = {
      designation: '',
      codeBarre: '',
      laboratoire: '',
    };
    this.searchProduitWinplusModel={
      designation: '',
      codeBarre: '',
      laboratoire: '',
    }
    this.showSearchProduitGroupe = false;
    this.showSearchProduitWinplus = false;

  }


  






  ngOnInit() {
    this.initFilterForm();
    this.getAllAvailableSites();
  }


  loadProduitsSite() {
    this.initQueryParamsListener();
    this.produitSiteCriteria = this.isGroupePage ? { ...this.produitSiteCriteria,codeSite: [0] } : {...this.produitSiteCriteria, codeSite: this.sites.map(site => site.codeSite).filter(val => val != 0) };
    !this.isFilterQueryParams && this.getSiteProducts();
  }


  onSiteChange(event) {
    this.filterForm.get('codeSite').patchValue(event);
  }
  
    initQueryParamsListener(){
      // ?nouveaux=true&interval=1m&target=groupe
  
      this.route.queryParams.subscribe(params => {
        const newParam = params['nouveaux'];
        const intervalParam = params['interval'];
        const targetParam = params['target'];
        const codeGroupe = params['codeGroupe'];
        if(codeGroupe){
           this.isFilterQueryParams = true;
          this.filterForm.get('codeGroupe').patchValue(codeGroupe);
          this.filterSubmit();
          return;
        }
        if(!newParam || !intervalParam || !targetParam) {return;}
        
        
         this.isFilterQueryParams = true;
         const dates = this.getIntervalStartEndDate(intervalParam);
         const produitSiteCriteria = new  ProduitSiteCriteriaForm({
          dateDFPDu: dates.dateDebut,
          dateDFPAu: dates.dateFin,
          transcoCodeGroupe: targetParam === 'groupe' ? false : null,
          transcoWinplus: targetParam === 'winplus' ? false : null,
          codeSite: targetParam === 'winplus' ? [0] : this.sites.map(site => site.codeSite).filter(code => code !== 0),
          estActif: targetParam === 'winplus' ? true : null,
         })

          this.filterForm.get('dateDFPDu').patchValue(moment(dates.dateDebut));
          this.filterForm.get('dateDFPAu').patchValue(moment(dates.dateFin));
          this.filterForm.get('transcoCodeGroupe').patchValue(targetParam === 'groupe' ? false : null);
          this.filterForm.get('transcoWinplus').patchValue(targetParam === 'winplus' ? false : null);
          this.filterForm.get('estActif').patchValue(targetParam === 'winplus' ? true : null);

         const cleanedCriteria = this.cleanEmptyValues(produitSiteCriteria);
          this.produitSiteCriteria = cleanedCriteria;
          this.navigation.skip = 0;
          this.getSiteProducts();
      })
    }
  
  
        getIntervalStartEndDate(interval: string) {
          const now = moment();
          let dateDebut: moment.Moment;
          const dateFin = now.clone().endOf('day');
        
          dateDebut = now.clone().subtract(
            interval === 'today' ? 0 :
            interval === '7d' ? 7 :
            interval === '15d' ? 15 :
            interval === '1m' ? 1 : 0,
            interval === '1m' ? 'month' : 'days'
          ).startOf('day');
        
          return {
            dateDebut: dateDebut.format('YYYY-MM-DD HH:mm:ss'),
            dateFin: dateFin.format('YYYY-MM-DD HH:mm:ss'),
          }
        }
  


  initFilterForm() {
    this.filterForm = this.fb.group({
      codeSite: [],
      codeProSite: [],
      designation: [null],
      codeWinplus: [null],
      codeGroupe: [null],
      dateCreateDu: [null],
      dateCreateAu: [null],
      dateDFPDu: [null],
      dateDFPAu: [null],
      codeBarre: [null],
      labelleLabo: [null],
      verified: [null],
      id: [null],
      transcoWinplus: [null],
      transcoCodeGroupe: [null],
      produitSupp: [null],
      codeSophatel:[null],
      envoyer: [null],
      estActif: [null],

    });
  }

  getSiteProducts() {
    this.productsService.getProduitsSiteByCriteria(this.navigation, this.produitSiteCriteria).subscribe((res) => {
      this.siteProducts.data = res?.content;
      this.siteProducts.total = res?.totalElements;
      this.mapSiteLabelToProduitSite(this.siteProducts.data);
    });
  }

  private mapSiteLabelToProduitSite(listProduit: ProduitSite[]) {
    listProduit.forEach((produitSite: ProduitSite) => {
      produitSite['siteLabel'] = CodeSites.find((site) => site.code_site == produitSite.codeSite)?.nom;
    })
  }

  onSortChange(sort: SortDescriptor[]) {
    this.siteProductSort = sort;
    if (this.siteProductSort && this.siteProductSort.length > 0 && this.siteProductSort[0].dir) {
      this.navigation.sortField = sort[0].field;
      this.navigation.sortMethod = sort[0].dir;
      this.getSiteProducts();
    } else {
      this.navigation.sortField = null;
      this.navigation.sortMethod = null;
      this.getSiteProducts();
    }
  }


  clearFilter() {
    this.filterForm.reset();
    this.produitSiteCriteria = this.isGroupePage ? { codeSite: [0] } : { codeSite: this.sites.map(site => site.codeSite).filter(val => val != 0) };
    this.navigation.skip = 0;
    this.selectedSites = [];
    this.customSelect.clearState();
    this.getSiteProducts();
    this.showFilter = false;
  }


  filterSubmit() {
    this.showFilter = false;
    const filterValues = this.filterForm.value;
    const criteria = this.filterToCriteria(filterValues);
    const cleanedCriteria = this.cleanEmptyValues(criteria);
    this.produitSiteCriteria = cleanedCriteria;
    this.navigation.skip = 0;
    this.getSiteProducts();
  }

  isValidateDate(date :string){
    return moment(date).isValid() && typeof date != 'undefined' && date != null
  }

  getAllAvailableSites(){
  this.productsService.sites$.subscribe({
    next: (res)=>{
      this.sites = res;
      this.selectSites = this.sites.map(site => ({
        label: site.nom,
        data: site,
        value: site.codeSite
      }));
      this.loadProduitsSite();
    }
  })
  }



  filterToCriteria(filterValues: any): ProduitSiteCriteriaForm {
    const criteria = new ProduitSiteCriteriaForm({
      designation: filterValues.designation,
      codeGroupe: filterValues.codeGroupe,
      codeProSite: filterValues.codeProSite,
      codeWinplus: filterValues.codeWinplus,
      transcoCodeGroupe: filterValues.transcoCodeGroupe,
      transcoWinplus: filterValues.transcoWinplus,
      produitSupp: filterValues.produitSupp,
      dateCreateDu: this.isValidateDate(filterValues.dateCreateDu) ? moment(filterValues.dateCreateDu).format('YYYY-MM-DD HH:mm:ss') : null,
      dateCreateAu: this.isValidateDate(filterValues.dateCreateAu) ? moment(filterValues.dateCreateAu).format('YYYY-MM-DD HH:mm:ss') : null,
      codeBarre: filterValues.codeBarre,
      labelleLabo: filterValues.labelleLabo,
      codeSophatel: filterValues.codeSophatel,
      envoyer: filterValues.envoyer,
      estActif: filterValues.estActif,
      
    });
    if (this.isGroupePage) {
      criteria.codeSite = [0];
    } else {
       if(!filterValues.codeSite || filterValues.codeSite.length === 0) {
        criteria.codeSite = this.sites.map(site => site.codeSite).filter(code => code !== 0);
       }else{
        criteria.codeSite = filterValues.codeSite;
       }
    }
    return criteria;
  }

  cleanEmptyValues<T>(filterValues: T): T {
    const cleanedValues = { ...filterValues };
    Object.keys(cleanedValues).forEach(key => {
      if (cleanedValues[key] === null || cleanedValues[key] === undefined || typeof cleanedValues[key] === 'string' && cleanedValues[key].trim() === '') {
        delete cleanedValues[key];
      }
    });
    return cleanedValues;
  }

  showSearchProductGroupe() {
    this.showSearchProduitGroupe = true;
    
  }


  navigateToTranscodeWinplus() {
    if(this.isGroupePage){
      // here i need to show winplus search widget
      this.showSearchProduitWinplus = true;
      this.showSearchProduitGroupe = false;
      this.winplusProductNavigation.skip = 0;
      this.searchProduitWinplusModel = {
        designation: '',
        codeBarre: '',
        laboratoire: '',
      };
      this.winplusProducts.data = [];
      this.winplusProducts.total = 0;

    }else{
      this.router.navigate(['/references/ref-produits/produits/produits-groupes'],{
        queryParams: {
          codeGroupe: this.ProductGroupeTranscoded[0]?.codeGroupe,
        }

      });
    }

  }



  processTranscode(dataItem) {
    this.userInputService.confirm("Transcoder le produit ?",
       `vous êtes sur le point de transcoder le produit Site: ${this.clickedItem?.designation} a produit Winplus ${dataItem.designation} (${dataItem.codeWinplus})`,
       "oui Transcoder", "non", BootstrapColorClasses.success).then((res) => {
      if (res) {
        // this.updateProduitSiteTransco({ codeGroupe, codeWinplus, id });
      console.log('Processing transcode for:', dataItem);
      this.updateProduitSiteTransco(dataItem);
      }
    }).catch(()=>{})


  }

  searchProductGroupe() {
    this.produitGroupeCriteria = this.cleanEmptyValues(this.searchProduitGroupeModel);
    this.isSearchGroupeLoading = true;
    this.productsService.getProduitsSiteByCriteria(this.groupeProductNavigation, {
      codeSite: [0],
      ...this.produitGroupeCriteria
    })
    .subscribe({
      next: (res) => {
        this.groupeProducts.data = res?.content;
        this.groupeProducts.total = res?.totalElements;
        // this.mapSiteLabelToProduitSite(this.groupeProducts.data);
      },
      complete: () => {
        this.isSearchGroupeLoading = false;
      }
    })
  }

  searchProductWinplus(){
    this.winplusSearchMode = 'manual';
    this.winplusProductNavigation.pageSize = 25;
    if (!this.searchProduitWinplusModel.designation && !this.searchProduitWinplusModel.codeBarre) {
      this.alertService.warning("Veuillez remplir au moins un critère de recherche", "Recherche Winplus");
      return;
    }
    this.produitWinplusCriteria = this.cleanEmptyValues(this.searchProduitWinplusModel);
    this.isWinplusSearchLoading = true;
    this.productsService.getProduitsWinplusByCriteria(this.winplusProductNavigation,this.produitWinplusCriteria)
    .subscribe({
      next: (res) => {
        this.winplusProducts.data = res?.content;
        this.winplusProducts.total = res?.totalElements;
        // this.mapSiteLabelToProduitSite(this.groupeProducts.data);
      },
      complete: () => {
        this.isWinplusSearchLoading = false;
      }
    })
  }
  HandleEditProduitGroupeDesignation(produitSite: ProduitSite) {
    this.productEditDesignation = produitSite.designation;
    const modalref = this.modalService.open(this.editDesignationModal, { size: 'lg', backdrop: 'static' });
    this.clearClickedItem();
    this.clickedItem = produitSite;
    this.clickedItem['clicked'] = true;
    modalref.closed.subscribe(() => {
      this.productEditDesignation = '';
      this.clickedItem = null;
      // Clear suggestions when modal closes
      this.clearDesignationSuggestions();
    });
  }

  // Clear all designation suggestions and search data
  clearDesignationSuggestions() {
    this.designationSuggestions = [];
    this.winplusDesignationSearchResults = [];
    this.winplusDesignationSearchModel.designation = '';
    this.showWinplusDesignationSearch = false;
    this.isLoadingDesignationSuggestions = false;
    this.isLoadingWinplusDesignationSearch = false;
  }

  // Get AI suggestions for designation
  getWinplusDesignationSuggestions() {
    if (!this.clickedItem) return;

    
    const tapCriteria = new TAPCriteria({
      designation: this.clickedItem.designation,
      barcode: this.clickedItem.codeBarre,
    });
    
    this.isLoadingDesignationSuggestions = true;
    this.showWinplusDesignationSearch = false;
    this.designationSuggestions = [];
    
    this.productsService.searchProduitByAssistant([tapCriteria])
      .pipe(
        retry(2) // Retry up to 2 times if the request fails
      )
      .subscribe({        next: (res) => {
          this.designationSuggestions = res.associations.map((association) => new ProduitWinplus({
            designation: association.nom_base_winplus,
            codeWinplus: association.code_prd_winplus,
            codeGroupe: association.code_groupe,
            prixVenteStd: association.ppv_winplus,
            prixAchatStd: association.pph_winplus,
            codeBarre: association.code_barre_win,
          })).slice(0, 5); // Limit to 5 suggestions
        },
        error: (err) => {
          this.alertService.error("Erreur lors de la récupération des suggestions", "Erreur AI");
        },
        complete: () => {
          this.isLoadingDesignationSuggestions = false;
        }
      });
  }

  // Toggle manual search section
  toggleWinplusDesignationSearch() {
    this.showWinplusDesignationSearch = !this.showWinplusDesignationSearch;
    if (this.showWinplusDesignationSearch) {
      this.winplusDesignationSearchResults = [];
      this.winplusDesignationSearchModel.designation = '';
    }
  }

  // Search Winplus products by designation for suggestions
  searchWinplusForDesignation() {
    if (!this.winplusDesignationSearchModel.designation.trim()) {
      this.alertService.warning("Veuillez saisir une désignation à rechercher", "Recherche");
      return;
    }

    this.isLoadingWinplusDesignationSearch = true;

    this.winplusDesignationSearchResults = [];

    const criteria = {
      designation: this.winplusDesignationSearchModel.designation.trim()
    };

    this.productsService.getProduitsWinplusByCriteria({
      pageSize: 10,
      skip: 0
    }, criteria).subscribe({
      next: (res) => {
        this.winplusDesignationSearchResults = res.content || [];
      },
      error: (err) => {
        this.alertService.error("Erreur lors de la recherche", "Erreur");
      },
      complete: () => {
        this.isLoadingWinplusDesignationSearch = false;
      }
    });
  }

  // Use a suggestion for the designation
  useDesignationSuggestion(designation: string) {
    if (designation && designation.length <= 30) {
      this.productEditDesignation = designation;
    } else if (designation.length > 30) {
      this.alertService.warning("Cette désignation dépasse 30 caractères", "Désignation trop longue");
    }
  }

  updateProduitGroupeDesignation(modal){
    const produitSite = this.clickedItem;
    if(!this.productEditDesignation || this.productEditDesignation.trim() === '') {
      this.alertService.error("La désignation du produit ne peut pas être vide", "Erreur de mise à jour");
      return;
    }
      if(this.productEditDesignation?.length >30) {
        this.alertService.error("La désignation du produit ne peut pas dépasser 30 caractères", "Erreur de mise à jour");
        return;
      }
      if( !produitSite || !produitSite.designation) {
      this.alertService.error("Produit non trouvé", "Erreur de mise à jour");
      return;
    }
         const updatedProduitSite = new ProduitSite({
          ...produitSite,
          designation: this.productEditDesignation?.trim(),
        });
        this.productsService.updateOrCreateProduitGroupe(updatedProduitSite).subscribe({
          next: (res) => {
            this.alertService.success("Produit mis à jour avec succès", "Mise à jour du produit");
            this.siteProducts.data = this.siteProducts.data.map(item => {
              if (item.idSource === res.idSource && item.codeSite === res.codeSite) {
                return { ...item, designation: res.designation, clicked: true};
              }
              return item;
            });
            modal.close();
          },
          error: (err) => {
            this.alertService.error("Erreur lors de la mise à jour du produit", "Erreur");
          }
        });
  }


  searchProductWinplusByTap(){
    if (!this.clickedItem?.designation && this.clickedItem?.designation?.trim() === '') {
      this.alertService.warning("Veuillez sélectionner un produit avant de lancer la recherche", "Recherche Winplus");
      return;
    }
    this.winplusSearchMode = 'tap'; 
    this.winplusProductNavigation.pageSize = 100;
    const tapCriteria = new TAPCriteria({
      designation : this.clickedItem?.designation,
      barcode: this.clickedItem?.codeBarre,
    })
    this.isWinplusSearchLoading = true;
    this.productsService.searchProduitByAssistant([tapCriteria])
      .pipe(
        retry(2) // Retry up to 2 times if the request fails
      )
      .subscribe({
        next: (res) => {
          const listProduitWinplus = res.associations.map((association) => new ProduitWinplus({
            designation: association.nom_base_winplus,
            codeWinplus: association.code_prd_winplus,
            codeGroupe: association.code_groupe,
            prixVenteStd: association.ppv_winplus,
            prixAchatStd: association.pph_winplus,
            codeBarre: association.code_barre_win,
          }))
          this.winplusProducts.data = listProduitWinplus;
          this.winplusProducts.total = listProduitWinplus.length;
        },
        complete: () => {
          this.isWinplusSearchLoading = false;
        }
      })
  }


  openTranscoDrawer(produitSite: ProduitSite) {
    this.clearClickedItem();
    this.clickedItem = produitSite;
    this.clickedItem['clicked'] = true;
    this.showTranscoDrawer = true;
    this.getProductGroupeTranscoded();
    this.showSearchProduitWinplus = false;
  }

  clearClickedItem() {
      this.siteProducts.data.forEach(item => {
        delete item['clicked'];
      });
  }

  getAllProductSiteLinkedToGroupe(){
    const produitSiteCriteria = new ProduitSiteCriteriaForm({
      codeGroupe: this.ProductGroupeTranscoded[0]?.codeGroupe,
      codeSite: CodeSites.map(site => site.code_site).filter(code => code !== 0),
    })
    this.isLoadingsearchLinkedProductSite = true;
    this.isFetchedLinkedProductSite = false;
    this.productsService.getProduitsSiteByCriteria(this.groupeProductNavigation, produitSiteCriteria).subscribe({
      next: (res) => {
        this.productSiteLinkedToGroupe = res.content;
        if(res.content.length === 0){
          this.isFetchedLinkedProductSite = true;
        }
      },
      complete: () => {
        this.isLoadingsearchLinkedProductSite = false;
      }
    });

  }

  getProductGroupeTranscoded() {
    this.isTranscoGroupeLoading = true;
    if (!this.clickedItem?.codeGroupe) {
      this.isTranscoGroupeLoading = false;
      return;
    }

    this.productsService.getProduitsSiteByCriteria({
      pageSize: 21,
      skip: 0,
    }, {
      codeSite: [0],
      codeGroupe: this.clickedItem?.codeGroupe,
    }).subscribe({
      next: (res) => {
        this.ProductGroupeTranscoded = res.content;
        this.isTranscoGroupeLoading = false;
        console.log('ProductGroupeTranscoded', this.ProductGroupeTranscoded);

        if (res.content?.length > 0) {
          this.getProductWinplusTranscoded();
        }
      },
      complete: () => {
        this.isTranscoGroupeLoading = false;
      }
    });
  }

  handleMarkForProcess(produitSite: ProduitSite) {
    const processToggleValue = produitSite.dateDeclarationForProcess ? true : false;

    this.userInputService.confirm(
      `${processToggleValue ? 'Dé-marquer' : 'Marquer'} Produit Pour Traitement`, 
      `Voulez-vous vraiment ${processToggleValue ? 'dé-marquer' : 'marquer'} le produit ${produitSite.designation} pour traitement ?`, 
      `${processToggleValue ? 'Dé-marquer' : 'Marquer'} Le Produit`, "Annuler",
    processToggleValue ? BootstrapColorClasses.danger : BootstrapColorClasses.success
    )
      .then((res) => {
        if (res) {
          this.productsService.makeProductSiteForProcess(produitSite, !processToggleValue).subscribe((res) => {
            this.alertServ.success(`Produit ${processToggleValue ? 'dé-marqué' : 'marqué'} pour traitement avec succès !`)
            this.getSiteProducts();
          }, (err) => {
            this.alertServ.error("Erreur lors de la mise à jour du produit !")
          })
        }
      }).catch(() => { })

  }

  rowClass(args: RowClassArgs) {
    if (args.dataItem?.clicked) {
      return { 'highlight-row-clicked': true };
    }
    return '';
  }

  compareFn(a, b) {
    return a == b;
  }

  onSortChangeProductSiteLinkedToGroupe(sort: SortDescriptor[]) {
    this.linkedProductSiteNavigation.sortField = sort[0]?.field;
    this.linkedProductSiteNavigation.sortMethod = sort[0]?.dir;
    this.productSiteLinkedToGroupeSort = sort;
  }

  toggleFilter(forcedValue: boolean = null) {
    this.showFilter = forcedValue !== null ? forcedValue : !this.showFilter;
  }

  pageChange(skip: number) {
    this.navigation.skip = skip;
    this.getSiteProducts();
  }
  
  SearchProductGroupePageChange(skip: number) {
    this.groupeProductNavigation.skip = skip;
    this.searchProductGroupe();
  }

  SearchProductWinplusPageChange(skip: number) {
    this.winplusProductNavigation.skip = skip;
    if (this.winplusSearchMode === 'manual') {
      this.searchProductWinplus();
    }  
  }


  detachProductGroupeTranscoded() {
    this.userInputService.confirm("Détacher le produit ?",
      `Vous êtes sur le point de détacher le produit Site: ${this.clickedItem?.designation} au produit Groupe ${this.ProductGroupeTranscoded[0]?.designation}`,
      "Oui détacher", "Non annuler", BootstrapColorClasses.danger).then((res) => {
        if (res) {
          this.updateProduitSiteTransco({ ...this.clickedItem, codeWinplus: null, codeGroupe: null });
        }
      }).catch(() => { })
  }


  getProductWinplusTranscoded() {
    this.isTranscoWinplusLoading = true;
    if (!this.clickedItem?.codeWinplus) {
      this.isTranscoWinplusLoading = false;
      return;
    }

    this.productsService.getProduitsWinplusByCriteria({
      pageSize: 21,
      skip: 0,
    }, {
      codeWinplus: this.clickedItem?.codeWinplus,
    }).subscribe({
      next: (res) => {
        this.ProductWinplusTranscoded = res.content;
        this.isTranscoWinplusLoading = false;
      },
      complete: () => {
        this.isTranscoWinplusLoading = false;
      }
    });
  }


    updateProduitGroupeWithTranscodeWinplus(produitWinplus: ProduitWinplusDto) {
    const produitSite = new ProduitSite({
      ...this.ProductGroupeTranscoded[0],
      codeWinplus: produitWinplus.codeWinplus,
      codeSite:0
    })
    this.userInputService.confirm("Transcoder le produit Winplus", "Êtes-vous sûr de vouloir transcodé le produit Winplus ?","oui","non")
    .then((res) => {
            if(res){

              this.productsService.updateOrCreateProduitGroupe(produitSite).subscribe({
            next: (response) => {
              this.ProductGroupeTranscoded[0].codeWinplus = produitWinplus.codeWinplus;
              this.ProductWinplusTranscoded = [produitWinplus];
              this.clickedItem = response;
              this.showSearchProduitWinplus = false;
              this.winplusProducts.data = [];
              this.winplusProducts.total = 0;
              this.getProductWinplusTranscoded();
              this.updateGridLigne(response);
            }
          });
      }
    }).catch(()=>{})

  }


    detacheWinplusProduit() {
    const produitSite = new ProduitSite({
      ...this.ProductGroupeTranscoded[0],
      codeWinplus: null,
      codeSite:0
    })

    this.userInputService.confirm("Détacher le produit Groupe", "Êtes-vous sûr de vouloir détacher le produit Groupe ?",
      "Oui détacher", "Non annuler",
    )
    .then((res) => {
      if(res){
          this.productsService.updateOrCreateProduitGroupe(produitSite).subscribe({
              next: (response) => {
                this.ProductWinplusTranscoded = null;
                this.clickedItem = response;
                this.updateGridLigne(response);
              }
            });
      }
    }).catch(()=>{})

  
  }



  private updateProduitSiteTransco(data: ProduitSite) {
    const produitSite = new ProduitSite({
      ...this.clickedItem,
      codeWinplus: data.codeWinplus,
      codeGroupe: data.codeGroupe,

    })
    this.productsService.updateOrCreateProduitSite(produitSite).subscribe({
      next: (res) => {
        this.clickedItem = res;
        this.showSearchProduitGroupe = false;
        this.groupeProducts.data = [];
        this.groupeProducts.total = 0;
        this.getProductGroupeTranscoded();
        this.updateGridLigne(res);
        this.alertService.success("Produit transcoder avec succès", "Produit transcoder")
      }

    })
  }

  private updateGridLigne(data: ProduitSite) {
    //  use composite key idSource + codeSite
    const index = this.siteProducts.data.findIndex(item => item.idSource === data.idSource && item.codeSite === data.codeSite);
    if (index !== -1) {
      const siteLabel = CodeSites.find((site) => site.code_site == data.codeSite)?.nom;
      this.siteProducts.data[index] = { clicked: true, siteLabel, ...data };
    }
    this.siteProducts.data = [...this.siteProducts.data];
  }



}
