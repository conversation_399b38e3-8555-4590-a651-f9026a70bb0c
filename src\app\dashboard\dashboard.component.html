<div class="bg-white px-2" style="min-height: calc(100vh - 55px);margin-left:-5px;">
  <!-- <div class="card card body text-center p-1 border-0 mb-3">
    <h1 class="m-0 text-black">Bienvenue</h1>
    <p class="m-0 text-black">Groupement sites(Groupe UGP).</p>
</div> -->


<div class="row">
  <div class="col-md-12">
    <!-- <div class="card card-body py-1 px-2 bg-white  shadow-lg  flex-row k-gap-2 align-content-center"
     style="border-radius: 10px;align-items: center; border: 1px solid #dfdfdf !important;">
      <i class="mdi mdi-account-switch k-text-success" style="font-size:36px !important;"></i>
      <h3 class="m-0 text-black fs-3" style="line-height: 1;" >
        Groupement <br/>
       <span class="text-muted fs-5"> Codification Client</span>
      </h3>
    </div> -->
    <div class="row mt-4 mb-3">
      <div class="col-md-3">
        <div data-label="Clients a traiter" class="dashboard-card stat-card success-card position-relative" style="align-items: flex-end;padding-top: 62px;">
          <div class="interval-container">
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauClientsInterval?.label === 'today'" (click)="refreshNewClientCount('today')">Aujourd'hui</span>
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauClientsInterval?.label === '7d'" (click)="refreshNewClientCount('7d')">7 jours</span>
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauClientsInterval?.label === '15d'" (click)="refreshNewClientCount('15d')">15 jours</span>
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauClientsInterval?.label === '1m'" (click)="refreshNewClientCount('1m')">1 mois</span>
          </div>
          <div class="interval-label">TODO</div>
            
            <div *ngIf="!isLoading && !isNouveauClientsLoading" class="stat-value">{{winclientStats?.nouveauxClients }}</div>
            <div *ngIf="isLoading || isNouveauClientsLoading"  class="skeleton-loading-block" ></div>
            <button title="Consulter Nouveaux Clients " 
            [attr.aria-label]="'Consulter Nouveaux Clients ' + nouveauClientsInterval?.label"
            (click)="exploreClientSiteWithIntervale()"
            class="btn btn-dark rounded-pill" role="button">
           Consulter  Clients a transcoder
          </button> 
           
        </div>
    </div>
      <div class="col-md-3">
        <div data-label="Produits Site a transcoder" class="dashboard-card stat-card primary-card position-relative" style="align-items: flex-end;padding-top: 62px;">
          <div class="interval-container">
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauProductsGroupeInterval?.label === 'today'" (click)="refreshTranscpdeCount('today','groupe')">Aujourd'hui</span>
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauProductsGroupeInterval?.label === '7d'" (click)="refreshTranscpdeCount('7d','groupe')">7 jours</span>
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauProductsGroupeInterval?.label === '15d'" (click)="refreshTranscpdeCount('15d','groupe')">15 jours</span>
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauProductsGroupeInterval?.label === '1m'" (click)="refreshTranscpdeCount('1m','groupe')">1 mois</span>
          </div>
          <div class="interval-label">TODO</div>
            
            <div *ngIf="!isLoading && isNouveauProduitLoadingLabel != 'groupe'" class="stat-value">{{produitStats?.totalProdSiteSansCodeGroupe }}</div>
            <div *ngIf="isLoading ||  isNouveauProduitLoadingLabel === 'groupe'"  class="skeleton-loading-block" ></div>
            <button title="Consulter Produits a transcoder "
            [attr.aria-label]="'Consulter Nouveaux Produits ' + nouveauProductsGroupeInterval?.label"
            (click)="exploreProductFournisseurWithIntervale('groupe')"
            class="btn btn-dark rounded-pill" role="button">
            Consulter Produits Site a transcoder
          </button>
           
        </div>
    </div>
      <div class="col-md-3">
        <div data-label="Produits Groupe a transcoder" class="dashboard-card stat-card warning-card position-relative" style="align-items: flex-end;padding-top: 62px;">
          <div class="interval-container">
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauProductsWinplusInterval?.label === 'today'" (click)="refreshTranscpdeCount('today','winplus')">Aujourd'hui</span>
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauProductsWinplusInterval?.label === '7d'" (click)="refreshTranscpdeCount('7d','winplus')">7 jours</span>
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauProductsWinplusInterval?.label === '15d'" (click)="refreshTranscpdeCount('15d','winplus')">15 jours</span>
            <span class="k-font-weight-bold"  role="button" [class.interval-active]="nouveauProductsWinplusInterval?.label === '1m'" (click)="refreshTranscpdeCount('1m','winplus')">1 mois</span>
          </div>
          <div class="interval-label">TODO</div>
            
            <div *ngIf="!isLoading && isNouveauProduitLoadingLabel != 'winplus'" class="stat-value">{{produitStats?.totalProdBaseGroupeSansCodeWinplus }}</div>
            <div *ngIf="isLoading ||  isNouveauProduitLoadingLabel === 'winplus'"  class="skeleton-loading-block" ></div>
            <button title="Consulter Nouveaux Clients "
            [attr.aria-label]="'Consulter Nouveaux Clients ' + nouveauProductsWinplusInterval?.label"
            (click)="exploreProductFournisseurWithIntervale('winplus')"
            class="btn btn-dark rounded-pill" role="button">
           Consulter Produits Groupe a transcoder
          </button>
           
        </div>
    </div>
      <div class="col-md-3">
        <div data-label="Anomalies Client Groupe" class="dashboard-card stat-card warning-card position-relative" style="align-items: flex-end;padding-top: 62px;">
          <div class="interval-label top-0">TODO</div>
            
            <div *ngIf="!isLoading && !isNouveauClientsLoading" class="stat-value">{{winclientStats?.totalAnomalies ?? 0 }}</div>
            <div *ngIf="isLoading || isNouveauClientsLoading"  class="skeleton-loading-block" ></div>
            <button title="Consulter Nouveaux Clients "
            [attr.aria-label]="'Consulter Nouveaux Clients ' + nouveauProductsWinplusInterval?.label"
            (click)="gotoAnomalies()"
            class="btn btn-dark rounded-pill" role="button">
           Consulter Les Anomalies Client Groupe
          </button>
           
        </div>
    </div>
  </div>  </div>  <div [ngClass]="{'col-md-6': !isAlertsExpanded, 'col-md-12': isAlertsExpanded}">
    <div class="card card-body py-1 px-2 bg-white  shadow-lg  flex-row k-gap-2 align-content-center"
    style="border-radius: 10px;align-items: center; border: 1px solid #dfdfdf !important;">     <div class="d-flex align-items-center">
       <button class="btn btn-link p-0 me-2 alert-expand-btn" 
               appTooltip="{{isAlertsExpanded ? 'Réduire la vue' : 'Étendre la vue'}}"
               (click)="toggleAlertsExpansion()">
         <i [class]="isAlertsExpanded ? 'mdi mdi-fullscreen-exit' : 'mdi mdi-fullscreen'" 
            style="font-size: 24px;"></i>
       </button>
       <i class="mdi mdi-alert-circle k-text-warning" style="font-size:36px !important;"></i>
       <h3 class="m-0 text-black fs-3 ms-2" style="line-height: 1;" >
         Alerts <br/>
         <span class="text-muted fs-5"> Alerts de modifications</span>
       </h3>
     </div><!-- Tab Navigation -->
     <div class="ms-auto d-flex align-items-center">
       <div class="alert-tab-container me-3">
         <div class="btn-group" aria-label="Alert tabs">
           <button class="btn btn-sm" 
                   [ngClass]="{'btn-primary': activeAlertTab === 'client', 'btn-outline-primary': activeAlertTab !== 'client'}" 
                   (click)="setActiveAlertTab('client')">
             Clients
           </button>
           <button class="btn btn-sm" 
                   [ngClass]="{'btn-primary': activeAlertTab === 'product', 'btn-outline-primary': activeAlertTab !== 'product'}" 
                   (click)="setActiveAlertTab('product')">
             Produits
           </button>
         </div>
       </div>
       
       <!-- Filter buttons for client alerts only -->
       <div class="alert-filter-container">
         <span class="me-2">Filtrer par état:</span>
        <div class="btn-group" aria-label="Filter alerts">
          <button class="btn btn-sm" 
                  [ngClass]="{'btn-info': (activeAlertTab === 'client' && alertFilter === 'tous') || (activeAlertTab === 'product' && produitFilter === 'tous'), 
                             'btn-outline-info': (activeAlertTab === 'client' && alertFilter !== 'tous') || (activeAlertTab === 'product' && produitFilter !== 'tous')}" 
                  (click)="activeAlertTab === 'client' ? setAlertFilter('tous') : changeFilterAlertProduit('tous')">Tous</button>
          <button class="btn btn-sm" 
                  [ngClass]="{'btn-info': (activeAlertTab === 'client' && alertFilter === 'ouverte') || (activeAlertTab === 'product' && produitFilter === 'ouverte'), 
                             'btn-outline-info': (activeAlertTab === 'client' && alertFilter !== 'ouverte') || (activeAlertTab === 'product' && produitFilter !== 'ouverte')}" 
                  (click)="activeAlertTab === 'client' ? setAlertFilter('ouverte') : changeFilterAlertProduit('ouverte')">Ouverte</button>
          <button class="btn btn-sm" 
                  [ngClass]="{'btn-info': (activeAlertTab === 'client' && alertFilter === 'fermer') || (activeAlertTab === 'product' && produitFilter === 'fermer'), 
                             'btn-outline-info': (activeAlertTab === 'client' && alertFilter !== 'fermer') || (activeAlertTab === 'product' && produitFilter !== 'fermer')}" 
                  (click)="activeAlertTab === 'client' ? setAlertFilter('fermer') : changeFilterAlertProduit('fermer')">Fermer</button>
        </div>
       </div>
     </div>
   </div>    <!-- Client Alerts Grid -->
    <div class="col-12 card card-body p-0 border-0" style="overflow: hidden; border-radius: 10px;" *ngIf="activeAlertTab === 'client'">
      <kendo-grid [data]="winclientAlert" class="ref-grid header-wrap content-wrap position-relative" [pageable]="true"
      [pageSize]="navigation.pageSize"
      [skip]="navigation.skip" [loading]="isLoading" [height]="500">
        <kendo-grid-column field="siteLabel" title="Site"[width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span>{{dataItem.codeSite | sitePipe}}</span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="cliCode" [title]="'C.Code'"[width]="60"></kendo-grid-column>
        <kendo-grid-column field="champModifie" title="Champ Modifié" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="ancienneValeur" title="Ancienne Valeur" [width]="150"></kendo-grid-column>
        <kendo-grid-column field="nouvelleValeur" title="Nouvelle Valeur" [width]="150"></kendo-grid-column>
        <kendo-grid-column field="dateAlerte" title="Date d'Alerte" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span>{{dataItem.dateAlerte | date:'dd/MM/yyyy'}}</span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="etat" title="État" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span [ngClass]="{'text-success': dataItem.etat === 1, 'text-danger': dataItem.etat === 0}">
              {{dataItem.etat === 1 ? 'férmée' : 'ouverte'}}
            </span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="dateClotureAlerte" title="Date Clôture" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span *ngIf="dataItem.dateClotureAlerte">{{dataItem.dateClotureAlerte | date:'dd/MM/yyyy'}}</span>
            <span *ngIf="!dataItem.dateClotureAlerte" class="text-gray">non disponible</span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column title="" [width]="50" class="sicky-to-right" headerClass="sicky-to-right sicky-to-right-header">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-action-icon  [extendClass]="'circle-lg'" [icon]="'close'" backgroundColor="danger"  *ngIf="dataItem.etat !== 0" (click)="changeAlertStatus(dataItem)"></app-action-icon>
            <app-action-icon  [extendClass]="'circle-lg'" [icon]="'check'"  backgroundColor="success"  *ngIf="dataItem.etat === 0" (click)="changeAlertStatus(dataItem)"></app-action-icon>
          </ng-template>
        </kendo-grid-column>
        <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
        let-total="total">
        <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
          [navigation]="navigation" style="width: 100%;"
          (pageChange)="pageChange($event)"></wph-grid-custom-pager>
      </ng-template>
      </kendo-grid>
    </div>    <!-- Product Alerts Grid -->
    <div class="col-12 card card-body p-0 border-0" style="overflow: hidden; border-radius: 10px;" *ngIf="activeAlertTab === 'product'">
      <kendo-grid [data]="alertProduit" class="ref-grid header-wrap content-wrap position-relative" [pageable]="true"
      [pageSize]="alertProduitNavigation.pageSize"
      [skip]="alertProduitNavigation.skip" [loading]="isLoading" [height]="500">
        <kendo-grid-column field="codeSite" title="Site" [width]="70">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span>{{dataItem.codeSite | sitePipe}}</span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="proCode" title="P.Code" [width]="80"></kendo-grid-column>
        <kendo-grid-column field="champModifie" title="Champ Modifié" [width]="100"></kendo-grid-column>
        <kendo-grid-column field="ancienneValeur" title="Ancienne Valeur" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span [title]="dataItem.ancienneValeur">
              {{dataItem.ancienneValeur | slice:0:50}}{{dataItem.ancienneValeur?.length > 50 ? '...' : ''}}
            </span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="nouvelleValeur" title="Nouvelle Valeur" [width]="200">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span [title]="dataItem.nouvelleValeur">
              {{dataItem.nouvelleValeur | slice:0:50}}{{dataItem.nouvelleValeur?.length > 50 ? '...' : ''}}
            </span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="dateAlerte" title="Date d'Alerte" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span>{{dataItem.dateAlerte | date:'dd/MM/yyyy'}}</span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column field="dateClotureAlerte" title="Date Clôture" [width]="100">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span *ngIf="dataItem.dateClotureAlerte">{{dataItem.dateClotureAlerte | date:'dd/MM/yyyy'}}</span>
            <span *ngIf="!dataItem.dateClotureAlerte" class="text-gray">non disponible</span>
          </ng-template>
        </kendo-grid-column>
        <kendo-grid-column  title="Action" [width]="90">
          <ng-template kendoGridCellTemplate let-dataItem>
            <app-action-icon [extendClass]="'circle-lg'" [icon]="'close'" backgroundColor="danger" *ngIf="dataItem.dateClotureAlerte" (click)="changeAlertStatusProduit(dataItem)"></app-action-icon>
            <app-action-icon [extendClass]="'circle-lg'" [icon]="'check'" backgroundColor="success" *ngIf="!dataItem.dateClotureAlerte" (click)="changeAlertStatusProduit(dataItem)"></app-action-icon>
          </ng-template>
           
        </kendo-grid-column>
        <ng-template kendoPagerTemplate let-totalPages="totalPages" let-currentPage="currentPage"
        let-total="total">
        <wph-grid-custom-pager [totalElements]="total" [totalPages]="totalPages" [currentPage]="currentPage"  [allowPageSizes]="false"
          [navigation]="alertProduitNavigation" style="width: 100%;"
          (pageChange)="pageChangeProduit($event)"></wph-grid-custom-pager>
      </ng-template>      </kendo-grid>    </div>
  </div>
  <div [ngClass]="{'col-md-6': !isAlertsExpanded, 'col-md-12': isAlertsExpanded}">
    <div class="card card-body border-0  py-1 px-2  bg-white shadow-lg flex-row k-gap-2 align-items-center"  
    style="border-radius: 10px;align-items: center; border: 1px solid #dfdfdf !important;">
    <div style="display: contents;">
      <i class="mdi mdi-update k-text-primary"  style="font-size:36px !important;"></i>
      <h3 class="m-0 text-black fs-3" style="line-height: 1;" >
        Mise à jour <br/>
       <span class="text-muted fs-5">Monitoring extraction</span>
      </h3>
    </div>
        <button type="button" class="rounded-pill ms-auto bg-dark d-flex align-items-center justify-content-center px-2" style="height: 40px; border:none;" (click)="executeBatchMajProduit()">
          <i class="mdi mdi-refresh text-white" style="font-size: 20px; line-height: 1;"></i>
          <span class="text-white ms-1">Actualiser Produits</span>
      </button>
        <button type="button" class="rounded-pill bg-dark d-flex align-items-center justify-content-center px-2" style="height: 40px; border:none;" (click)="executeBatchImportClientSite()">
          <i class="mdi mdi-refresh text-white" style="font-size: 20px; line-height: 1;"></i>
          <span class="text-white ms-1">Actualiser clients sites</span>
      </button>
      </div>
    <div class="row ms-1">
      <div class="col-6 card card-body p-0 border-0" style="overflow: hidden; border-radius: 10px;">
        <kendo-grid [kendoGridBinding]="sitesMajStats" class="ref-grid" [loading]="isLoading" [height]="500">
                      <ng-template kendoGridToolbarTemplate>
    <div [ngClass]="{'client-have-association-bg':false,'client-have-no-association-bg':true}" style="height: 30px;" class="d-flex justify-content-between align-items-center px-2">
      <span class="text-white fs-4 k-font-weight-bold">Monitoring Client</span>
    </div>
  </ng-template>
          <kendo-grid-column field="libelleLong" title="Site">
            <ng-template kendoGridCellTemplate let-dataItem>
              {{dataItem.id | sitePipe}}
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="derniereMaj" title="Date Maj Client">
            <ng-template kendoGridCellTemplate let-dataItem>
              <span *ngIf="dataItem.derniereMaj" [ngStyle]="{
              'color' : isDateToday(dataItem.derniereMaj) ? '#239500' : '#ff0202'
              }">{{dataItem.derniereMaj | date:'dd/MM/yyyy'}}</span>
              <span *ngIf="!dataItem.derniereMaj" class="text-gray">non disponible</span>
            </ng-template>
          </kendo-grid-column>
          <!-- <kendo-grid-column field="dateMajCommercial" title="Date Maj Commercial"></kendo-grid-column> -->
        </kendo-grid>
      </div>
      <div class="col-6 card card-body p-0 border-0" style="overflow: hidden; border-radius: 10px;">
        <kendo-grid [kendoGridBinding]="produitStats?.derniereMajParSite" class="ref-grid" [loading]="isLoading" [height]="500">
            <ng-template kendoGridToolbarTemplate>
    <div [ngClass]="{'client-have-association-bg':false,'client-have-no-association-bg':true}" style="height: 30px;" class="d-flex justify-content-between align-items-center px-2">
      <span class="text-white fs-4 k-font-weight-bold">Monitoring Produits</span>
    </div>
  </ng-template>
          <kendo-grid-column field="libelleSource" title="Site">
            <ng-template kendoGridCellTemplate let-dataItem>
              <span>{{dataItem.codeSite | sitePipe}}</span>
            </ng-template>
          </kendo-grid-column>
          <kendo-grid-column field="derniereMaj" title="Date Maj Client">
            <ng-template kendoGridCellTemplate let-dataItem>
                <span *ngIf="dataItem.dateMajFormatted && dataItem.dateMajFormatted !== 'non disponible'" [ngStyle]="{
                  'color' : isDateToday(dataItem.dateMajFormatted) ? '#239500' : '#ff0202'
                }">{{dataItem.dateMajFormatted | date:'dd/MM/yyyy'}}</span>
                <span *ngIf="!dataItem.dateMajFormatted || dataItem.dateMajFormatted === 'non disponible'" class="text-gray">non disponible</span>
            </ng-template>
          </kendo-grid-column>
          <!-- <kendo-grid-column field="dateMajCommercial" title="Date Maj Commercial"></kendo-grid-column> -->
        </kendo-grid>
      </div>
  </div>
  </div>
  

</div>
</div>
