import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { UsersService } from '../../services/users.service';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AlertService } from 'src/app/shared/services/alert.service';

@Component({
  selector: 'app-gestion-utilisateur',
  templateUrl: './gestion-utilisateur.component.html',
  styleUrls: ['./gestion-utilisateur.component.scss']
})
export class GestionUtilisateurComponent implements OnInit {

  @ViewChild('createUserModal') createUserModal: TemplateRef<any>;

  users = {
    data:[],
    total:0
  }
  navigation = {
    pageSize: 25,
    skip: 0,
    sortField: '',
    sortMethod: '',
  };
  sort: SortDescriptor[] = [];

  // Modal properties
  modalRef: NgbModalRef;
  createUserForm: FormGroup;
  roles: any[] = [];
  submitted = false;
  isEditMode = false;
  currentUser: any = null;

  constructor(
    private userService : UsersService,
    private modalService: NgbModal,
    private fb: FormBuilder,
    private alertService: AlertService
  ) { }

  ngOnInit() {
    this.getUsers();
    this.getRoles();
    this.initCreateUserForm();
  }


  getUsers(){
    this.userService.getUsersPaginated(this.navigation).subscribe(res => {
      this.users.data = res.content;
      this.users.total = res.totalElements;
    });
  }

  pageChange(skip: number){
    this.navigation.skip =skip;
    this.getUsers();
  }

  sortChange(sort: SortDescriptor[]) {
    this.sort = sort;
    this.navigation.sortField = sort[0]?.field;
    this.navigation.sortMethod = sort[0]?.dir;
    this.getUsers();
  }

  getRoles() {
    this.userService.getRoles().subscribe(res => {
      this.roles = res;
    });
  }

  initCreateUserForm() {
    this.createUserForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      firstname: ['', [Validators.required]],
      lastname: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      username: ['', [Validators.required]],
      role: [null, [Validators.required]]
    });
  }

  openCreateUser() {
    this.isEditMode = false;
    this.currentUser = null;
    this.submitted = false;
    this.createUserForm.reset();
    this.initCreateUserForm(); // Reinitialize form for create mode

    this.modalRef = this.modalService.open(this.createUserModal, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false
    });

    this.modalRef.result.then((result) => {
      if (result === 'save') {
        this.getUsers(); // Refresh the users list
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  openEditUser(user: any) {
    this.isEditMode = true;
    this.currentUser = user;
    this.submitted = false;

    // Initialize form for edit mode (password not required)
    this.createUserForm = this.fb.group({
      email: [user.email, [Validators.required, Validators.email]],
      firstname: [user.firstname, [Validators.required]],
      lastname: [user.lastname, [Validators.required]],
      newPassword: [''], // Password not required for edit
      username: [user.username, [Validators.required]],
      role: [user.role, [Validators.required]]
    });

    this.modalRef = this.modalService.open(this.createUserModal, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false
    });

    this.modalRef.result.then((result) => {
      if (result === 'save') {
        this.getUsers(); // Refresh the users list
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  onSubmitCreateUser() {
    this.submitted = true;

    if (this.createUserForm.valid) {
      const userData = this.createUserForm.value;
      userData.appClientId = 1;

      // If editing, add the user ID and handle password
      if (this.isEditMode && this.currentUser) {
        userData.id = this.currentUser.id;
        userData.idhash = this.currentUser.idhash;

        // If password is empty in edit mode, don't send it
        if (!userData.newPassword) {
          delete userData.newPassword;
        }
      }

      this.userService.createOrEditUser(userData).subscribe({
        next: (response) => {
          const message = this.isEditMode ? 'Utilisateur modifié avec succès' : 'Utilisateur créé avec succès';
          this.alertService.success(message);
          this.modalRef.close('save');
          this.getUsers();
        },
        error: (error) => {
          console.error('Error saving user', error);
          const message = this.isEditMode ? 'Erreur lors de la modification de l\'utilisateur' : 'Erreur lors de la création de l\'utilisateur';
          this.alertService.error(message);
        }
      });
    } else {
      this.alertService.warning('Veuillez remplir tous les champs requis');
    }
  }

  onCancelCreateUser() {
    this.modalRef.dismiss();
  }

}
