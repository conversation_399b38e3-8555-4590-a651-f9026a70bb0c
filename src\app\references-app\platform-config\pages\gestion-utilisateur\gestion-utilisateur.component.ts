import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { UsersService } from '../../services/users.service';
import { SortDescriptor } from '@progress/kendo-data-query';
import { AlertService } from 'src/app/shared/services/alert.service';

@Component({
  selector: 'app-gestion-utilisateur',
  templateUrl: './gestion-utilisateur.component.html',
  styleUrls: ['./gestion-utilisateur.component.scss']
})
export class GestionUtilisateurComponent implements OnInit {

  @ViewChild('createUserModal') createUserModal: TemplateRef<any>;

  users = {
    data:[],
    total:0
  }
  navigation = {
    pageSize: 25,
    skip: 0,
    sortField: '',
    sortMethod: '',
  };
  sort: SortDescriptor[] = [];

  // Modal properties
  modalRef: NgbModalRef;
  createUserForm: FormGroup;
  roles: any[] = [];
  submitted = false;

  constructor(
    private userService : UsersService,
    private modalService: NgbModal,
    private fb: FormBuilder,
    private alertService: AlertService
  ) { }

  ngOnInit() {
    this.getUsers();
    this.getRoles();
    this.initCreateUserForm();
  }


  getUsers(){
    this.userService.getUsersPaginated(this.navigation).subscribe(res => {
      this.users.data = res.content;
      this.users.total = res.totalElements;
    });
  }

  pageChange(skip: number){
    this.navigation.skip =skip;
    this.getUsers();
  }

  sortChange(sort: SortDescriptor[]) {
    this.sort = sort;
    this.navigation.sortField = sort[0]?.field;
    this.navigation.sortMethod = sort[0]?.dir;
    this.getUsers();
  }

  getRoles() {
    this.userService.getRoles().subscribe(res => {
      this.roles = res;
    });
  }

  initCreateUserForm() {
    this.createUserForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      firstname: ['', [Validators.required]],
      lastname: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(6)]],
      username: ['', [Validators.required]],
      role: [null, [Validators.required]]
    });
  }

  openCreateUser() {
    this.submitted = false;
    this.createUserForm.reset();
    this.modalRef = this.modalService.open(this.createUserModal, {
      size: 'lg',
      backdrop: 'static',
      keyboard: false
    });

    this.modalRef.result.then((result) => {
      if (result === 'save') {
        this.getUsers(); // Refresh the users list
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  onSubmitCreateUser() {
    this.submitted = true;

    if (this.createUserForm.valid) {
      const userData = this.createUserForm.value;

      this.userService.createOrEditUser(userData).subscribe({
        next: (response) => {
          this.alertService.success('Utilisateur créé avec succès');
          this.modalRef.close('save');
          this.getUsers();
        },
        error: (error) => {
          console.error('Error creating user', error);
          this.alertService.error('Erreur lors de la création de l\'utilisateur');
        }
      });
    } else {
      this.alertService.warning('Veuillez remplir tous les champs requis');
    }
  }

  onCancelCreateUser() {
    this.modalRef.dismiss();
  }

}
