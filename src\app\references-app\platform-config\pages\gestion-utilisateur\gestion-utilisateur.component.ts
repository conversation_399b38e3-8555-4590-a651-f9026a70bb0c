import { Component, OnInit } from '@angular/core';
import { UsersService } from '../../services/users.service';
import { SortDescriptor } from '@progress/kendo-data-query';

@Component({
  selector: 'app-gestion-utilisateur',
  templateUrl: './gestion-utilisateur.component.html',
  styleUrls: ['./gestion-utilisateur.component.scss']
})
export class GestionUtilisateurComponent implements OnInit {


  users = {
    data:[],
    total:0
  }
  navigation = {
    pageSize: 25,
    skip: 0,
    sortField: '',
    sortMethod: '',
  };
  sort: SortDescriptor[] = [];

  constructor(
    private userService : UsersService,
  ) { }

  ngOnInit() {
    this.getUsers();
  }


  getUsers(){
    this.userService.getUsersPaginated(this.navigation).subscribe(res => {
      this.users.data = res.content;
      this.users.total = res.totalElements;
    });
  }

  pageChange(skip: number){
    this.navigation.skip =skip;
    this.getUsers();
  }

  sortChange(sort: SortDescriptor[]) {
    this.sort = sort;
    this.navigation.sortField = sort[0]?.field;
    this.navigation.sortMethod = sort[0]?.dir;
    this.getUsers();
  }

  openCreateUser() {
    // Implement the logic to open the create user modal
  }

}
